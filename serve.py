#!/usr/bin/env python3
import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

# Change to the web build directory
web_dir = Path("build/web")
if web_dir.exists():
    os.chdir(web_dir)
    print(f"📁 Serving from: {web_dir.absolute()}")
else:
    print("❌ Web build directory not found!")
    exit(1)

PORT = 8080

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        super().end_headers()

    def log_message(self, format, *args):
        print(f"🌐 {format % args}")

try:
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"🚀 Nepali Date Converter running at:")
        print(f"   📱 http://localhost:{PORT}")
        print(f"   🌐 http://0.0.0.0:{PORT}")
        print(f"\n✨ Opening browser...")
        
        # Open browser
        webbrowser.open(f'http://localhost:{PORT}')
        
        print(f"🔄 Server running... Press Ctrl+C to stop")
        httpd.serve_forever()
        
except KeyboardInterrupt:
    print(f"\n🛑 Server stopped")
except Exception as e:
    print(f"❌ Error: {e}")
