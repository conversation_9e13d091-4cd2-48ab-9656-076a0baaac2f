import 'package:flutter/material.dart';
// import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  bool _isDarkMode = false;

  ThemeMode get themeMode => _themeMode;
  bool get isDarkMode => _isDarkMode;

  ThemeProvider() {
    _loadThemeMode();
  }

  void _loadThemeMode() async {
    // Temporarily disabled for APK build
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // bool isDark = prefs.getBool('isDarkMode') ?? false;
    // _isDarkMode = isDark;
    // _themeMode = isDark ? ThemeMode.dark : ThemeMode.light;
    // notifyListeners();
  }

  bool _isToggling = false;

  void toggleTheme() async {
    if (_isToggling) return; // Prevent multiple rapid toggles

    _isToggling = true;
    _isDarkMode = !_isDarkMode;
    _themeMode = _isDarkMode ? ThemeMode.dark : ThemeMode.light;

    // Temporarily disabled for APK build
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // await prefs.setBool('isDarkMode', _isDarkMode);

    notifyListeners();

    // Reset the toggle flag after a short delay
    await Future.delayed(const Duration(milliseconds: 200));
    _isToggling = false;
  }

  void setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    _isDarkMode = mode == ThemeMode.dark;

    // Temporarily disabled for APK build
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // await prefs.setBool('isDarkMode', _isDarkMode);

    notifyListeners();
  }
}
