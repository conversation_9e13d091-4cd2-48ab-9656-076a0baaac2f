import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/app_constants.dart';

class PrivacyPolicyPage extends StatefulWidget {
  const PrivacyPolicyPage({super.key});

  @override
  State<PrivacyPolicyPage> createState() => _PrivacyPolicyPageState();
}

class _PrivacyPolicyPageState extends State<PrivacyPolicyPage> {
  late final WebViewController _controller;
  bool _isLoading = true;
  bool _hasError = false;
  bool _webViewFailed = false;

  @override
  void initState() {
    super.initState();

    // On web platform, directly use Chrome Custom Tabs
    if (kIsWeb) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _openInChromeCustomTabs();
      });
    } else {
      // On mobile platforms, try WebView first
      _initializeWebView();
    }
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setUserAgent(
          'Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36')
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _hasError = false;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            print('WebView Error: ${error.description}');
            // If WebView fails, automatically switch to Chrome Custom Tabs
            _openInChromeCustomTabs();
          },
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      );

    // Add a timeout for WebView loading
    _loadWithTimeout();
  }

  void _loadWithTimeout() async {
    try {
      await _controller.loadRequest(Uri.parse(AppConstants.privacyPolicyUrl));

      // Set a timeout to detect if WebView is stuck
      Future.delayed(const Duration(seconds: 10), () {
        if (_isLoading && mounted) {
          print('WebView timeout - switching to Chrome Custom Tabs');
          _openInChromeCustomTabs();
        }
      });
    } catch (e) {
      print('WebView load error: $e');
      _openInChromeCustomTabs();
    }
  }

  Future<void> _openInChromeCustomTabs() async {
    if (_webViewFailed) return; // Prevent multiple calls

    setState(() {
      _webViewFailed = true;
      _isLoading = false;
      _hasError = false;
    });

    try {
      final Uri url = Uri.parse(AppConstants.privacyPolicyUrl);

      // Try to launch in Chrome Custom Tab
      final bool launched = await launchUrl(
        url,
        mode: LaunchMode.inAppBrowserView,
        browserConfiguration: const BrowserConfiguration(
          showTitle: true,
        ),
      );

      if (!launched) {
        // Fallback to external browser
        await launchUrl(
          url,
          mode: LaunchMode.externalApplication,
        );
      }

      // Return to home screen after opening Chrome Custom Tab
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      print('Chrome Custom Tabs error: $e');
      // If everything fails, show error
      if (mounted) {
        setState(() {
          _hasError = true;
        });
      }
    }
  }

  void _retry() {
    setState(() {
      _hasError = false;
      _isLoading = true;
      _webViewFailed = false;
    });
    _loadWithTimeout();
  }

  void _openInBrowser() {
    _openInChromeCustomTabs();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF121212) : Colors.white,
      appBar: AppBar(
        title: const Text(
          'Privacy Policy',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        backgroundColor:
            isDark ? const Color(0xFF1E1E1E) : const Color(0xFF87CEEB),
        foregroundColor: isDark ? Colors.white : Colors.black87,
        elevation: 2,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Stack(
        children: [
          // WebView (only on mobile platforms)
          if (!kIsWeb && !_hasError && !_webViewFailed)
            WebViewWidget(controller: _controller),

          // Loading Indicator
          if (_isLoading && !_hasError)
            Container(
              color: isDark ? const Color(0xFF121212) : Colors.white,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(Color(0xFF2D9CDB)),
                    ),
                    SizedBox(height: 16),
                    Text(
                      kIsWeb
                          ? 'Opening Privacy Policy in browser...'
                          : 'Loading Privacy Policy...',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Error State
          if (_hasError)
            Container(
              color: isDark ? const Color(0xFF121212) : Colors.white,
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Failed to load Privacy Policy',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Please check your internet connection and try again.',
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? Colors.grey[300] : Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton.icon(
                            onPressed: _retry,
                            icon: const Icon(Icons.refresh),
                            label: const Text('Retry'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2D9CDB),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                            ),
                          ),
                          ElevatedButton.icon(
                            onPressed: _openInBrowser,
                            icon: const Icon(Icons.open_in_browser),
                            label: const Text('Open in Browser'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF27AE60),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
