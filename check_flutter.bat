@echo off
title Flutter Path Verification
color 0A

echo.
echo ========================================
echo    FLUTTER PATH VERIFICATION
echo ========================================
echo.

echo [1/4] Checking if <PERSON>lut<PERSON> is in PATH...
flutter --version >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Flutter found in PATH!
    echo.
    echo Flutter Version:
    flutter --version
) else (
    echo ❌ Flutter not found in PATH
    echo.
    echo Please check if you added this path correctly:
    echo C:\Users\<USER>\Downloads\flutter_windows_3.32.0-stable\flutter\bin
    echo.
    echo Or try restarting your Command Prompt
    pause
    exit /b 1
)

echo.
echo [2/4] Checking Flutter Doctor...
flutter doctor

echo.
echo [3/4] Navigating to project...
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
echo Current directory: %CD%

echo.
echo [4/4] Checking if app can run...
echo.
echo ========================================
echo  Ready to run your app!
echo  Choose an option:
echo ========================================
echo.
echo [1] Run Flutter App (Web)
echo [2] Open HTML Preview
echo [3] Exit
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto run_flutter
if "%choice%"=="2" goto open_preview
if "%choice%"=="3" goto exit

:run_flutter
echo.
echo Starting Flutter app...
echo App will be available at: http://localhost:8080
echo.
flutter run -d web-server --web-port=8080
pause
goto end

:open_preview
echo.
echo Opening HTML preview...
start "" "preview.html"
echo ✅ Preview opened in browser!
pause
goto end

:exit
echo.
echo Goodbye!
goto end

:end
echo.
pause
