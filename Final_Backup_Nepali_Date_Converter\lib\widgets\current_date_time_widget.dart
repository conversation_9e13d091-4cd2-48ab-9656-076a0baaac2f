import 'package:flutter/material.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'dart:async';

class CurrentDateTimeWidget extends StatefulWidget {
  const CurrentDateTimeWidget({super.key});

  @override
  State<CurrentDateTimeWidget> createState() => _CurrentDateTimeWidgetState();
}

class _CurrentDateTimeWidgetState extends State<CurrentDateTimeWidget> {
  late Timer _timer;
  late DateTime _currentTime;
  late NepaliDateTime _currentNepaliTime;

  @override
  void initState() {
    super.initState();
    _updateTime();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTime();
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _updateTime() {
    setState(() {
      _currentTime = DateTime.now();
      _currentNepaliTime = NepaliDateTime.now();
    });
  }

  String _getNepaliDate() {
    try {
      final nepaliMonths = [
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON>',
        'Falgun',
        'Chaitra'
      ];

      final nepaliDays = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday'
      ];

      final dayName = nepaliDays[(_currentNepaliTime.weekday - 1) % 7];
      final monthName = nepaliMonths[_currentNepaliTime.month - 1];
      final day = _currentNepaliTime.day;
      final year = _currentNepaliTime.year;

      return '$dayName, $monthName $day, $year';
    } catch (e) {
      // Fallback to static display
      return 'Thursday, Jestha 15, 2082';
    }
  }

  String _getEnglishDateAndTime() {
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];

    final month = months[_currentTime.month - 1];
    final day = _currentTime.day;
    final year = _currentTime.year;

    final hour = _currentTime.hour;
    final minute = _currentTime.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);

    return '$month $day, $year – $displayHour:$minute $period';
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      children: [
        // Nepali Date - CAPITAL LETTERS (Display Only)
        Text(
          _getNepaliDate().toUpperCase(),
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
            letterSpacing: 0.5,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),

        // English Date and Time (Display Only)
        Text(
          _getEnglishDateAndTime(),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: isDark ? Colors.grey[300] : Colors.grey[700],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
