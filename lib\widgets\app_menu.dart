import 'package:flutter/material.dart';
import '../screens/privacy_policy_page.dart';

class AppMenu extends StatelessWidget {
  const AppMenu({super.key});

  void _showPrivacyPolicy(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const PrivacyPolicyPage(),
      ),
    );
  }

  void _showAboutApp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.info, color: Color(0xFF27AE60)),
            SizedBox(width: 8),
            Text('About App'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Nepali Date Converter & Age Calculator',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text('Version: 1.0.0'),
              SizedBox(height: 16),
              Text(
                'Features:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 4),
              Text('• Convert between Nepali (BS) and English (AD) dates'),
              Text('• Calculate age with detailed breakdown'),
              Text('• Dark/Light mode support'),
              Text('• Offline functionality'),
              Text('• Copy converted dates'),
              SizedBox(height: 16),
              Text(
                'This app helps you convert dates between Nepali and English calendars and calculate ages with precision.',
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _rateApp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.star, color: Colors.amber),
            SizedBox(width: 8),
            Text('Rate This App'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Do you like this app?',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
              ],
            ),
            SizedBox(height: 16),
            Text(
              'Your feedback helps us improve!',
              style: TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Thank you for your feedback!'),
                  backgroundColor: Color(0xFF27AE60),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF27AE60),
              foregroundColor: Colors.white,
            ),
            child: const Text('Rate 5 Stars'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: isDark ? Colors.grey[300] : const Color(0xFF4A90E2),
        size: 20,
      ),
      onSelected: (value) {
        switch (value) {
          case 'privacy':
            _showPrivacyPolicy(context);
            break;
          case 'rate':
            _rateApp(context);
            break;
          case 'about':
            _showAboutApp(context);
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'privacy',
          child: Row(
            children: [
              Icon(Icons.privacy_tip, color: Color(0xFF2D9CDB)),
              SizedBox(width: 12),
              Text('Privacy Policy'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'rate',
          child: Row(
            children: [
              Icon(Icons.star, color: Colors.amber),
              SizedBox(width: 12),
              Text('Rate This App'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'about',
          child: Row(
            children: [
              Icon(Icons.info, color: Color(0xFF27AE60)),
              SizedBox(width: 12),
              Text('About App'),
            ],
          ),
        ),
      ],
    );
  }
}
