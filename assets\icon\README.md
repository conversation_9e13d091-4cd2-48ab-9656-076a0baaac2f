# App Icon

Place your app icon file here as `app_icon.png` (1024x1024 pixels recommended).

The icon should represent the Nepali Date Converter app with:
- Calendar or date-related imagery
- Modern, clean design
- Good contrast for both light and dark backgrounds
- Nepali cultural elements (optional)

## Icon Requirements

### Android
- **Size**: 1024x1024 pixels
- **Format**: PNG with transparency
- **Style**: Material Design guidelines

### iOS
- **Size**: 1024x1024 pixels  
- **Format**: PNG
- **Style**: iOS Human Interface Guidelines

## Generating Icons

After placing your `app_icon.png` file in this directory, run:

```bash
flutter pub get
flutter pub run flutter_launcher_icons:main
```

This will automatically generate all required icon sizes for both Android and iOS platforms.
