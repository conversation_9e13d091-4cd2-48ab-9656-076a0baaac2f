<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nepali Date Converter & Age Calculator - Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f2f2f2 0%, #e8e8e8 100%);
            color: #333;
            min-height: 100vh;
        }

        .dark-mode {
            background: linear-gradient(135deg, #121212 0%, #1e1e1e 100%);
            color: #f5f5f5;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .dark-mode .container {
            background: #1e1e1e;
        }

        .app-bar {
            background: white;
            padding: 16px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dark-mode .app-bar {
            background: #1e1e1e;
            border-bottom-color: #333;
        }

        .app-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .dark-mode .app-title {
            color: #f5f5f5;
        }

        .settings-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #333;
        }

        .dark-mode .settings-btn {
            color: #f5f5f5;
        }

        .content {
            padding: 20px;
            padding-bottom: 80px;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .dark-mode .card {
            background: #2a2a2a;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #2D9CDB;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 24px;
            color: white;
        }

        .card-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .dark-mode .card-title {
            color: #f5f5f5;
        }

        .card-subtitle {
            color: #666;
            margin-top: 8px;
        }

        .dark-mode .card-subtitle {
            color: #aaa;
        }

        .date-picker {
            background: rgba(45, 156, 219, 0.1);
            border: 2px solid rgba(45, 156, 219, 0.3);
            border-radius: 12px;
            padding: 16px;
            margin: 16px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .date-picker:hover {
            background: rgba(45, 156, 219, 0.15);
        }

        .date-value {
            font-size: 24px;
            font-weight: bold;
            color: #2D9CDB;
            margin-bottom: 4px;
        }

        .date-details {
            color: #666;
            font-size: 14px;
        }

        .dark-mode .date-details {
            color: #aaa;
        }

        .conversion-arrow {
            text-align: center;
            margin: 16px 0;
        }

        .arrow-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: rgba(45, 156, 219, 0.1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #2D9CDB;
        }

        .result-card {
            background: linear-gradient(135deg, rgba(45, 156, 219, 0.1), rgba(39, 174, 96, 0.1));
            border: 2px solid rgba(45, 156, 219, 0.3);
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
        }

        .result-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #333;
        }

        .dark-mode .result-title {
            color: #f5f5f5;
        }

        .result-value {
            font-size: 28px;
            font-weight: bold;
            color: #2D9CDB;
            margin-bottom: 8px;
        }

        .age-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }

        .dark-mode .stat-card {
            background: rgba(42, 42, 42, 0.8);
        }

        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #2D9CDB;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }

        .dark-mode .stat-label {
            color: #aaa;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 400px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
        }

        .dark-mode .bottom-nav {
            background: #1e1e1e;
            border-top-color: #333;
        }

        .nav-item {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            color: #2D9CDB;
        }

        .nav-item:not(.active) {
            color: #666;
        }

        .nav-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 12px;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2D9CDB;
            color: white;
            border: none;
            border-radius: 50%;
            width: 56px;
            height: 56px;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(45, 156, 219, 0.3);
            z-index: 1000;
        }

        .hidden {
            display: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-in {
            animation: fadeIn 0.5s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="app-bar">
            <div class="app-title">Nepali Date Converter</div>
            <button class="settings-btn" onclick="toggleTheme()">⚙️</button>
        </div>

        <div class="content">
            <!-- Date Converter Tab -->
            <div id="converter-tab" class="tab-content">
                <div class="card animate-in">
                    <div class="card-header">
                        <div class="card-icon">🔄</div>
                        <div>
                            <div class="card-title">Date Converter</div>
                            <div class="card-subtitle">Convert between Nepali (BS) and English (AD) dates</div>
                        </div>
                    </div>
                </div>

                <div class="card animate-in">
                    <div class="date-picker" onclick="showDatePicker('english')">
                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                            <span style="font-size: 20px; margin-right: 8px;">📅</span>
                            <strong>English Date (AD)</strong>
                        </div>
                        <div class="date-value" id="english-date">26/05/2025</div>
                        <div class="date-details">Monday, May 26, 2025</div>
                    </div>

                    <div class="conversion-arrow">
                        <div class="arrow-icon">⇅</div>
                    </div>

                    <div class="date-picker" onclick="showDatePicker('nepali')">
                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                            <span style="font-size: 20px; margin-right: 8px;">📆</span>
                            <strong>Nepali Date (BS)</strong>
                        </div>
                        <div class="date-value" id="nepali-date">2082/02/11</div>
                        <div class="date-details">Sombar, Jestha 11, 2082</div>
                    </div>
                </div>

                <div class="result-card animate-in">
                    <div class="result-title">✅ Conversion Result</div>
                    <div style="margin-bottom: 16px;">
                        <div style="color: #666; font-size: 14px;">English Date (AD)</div>
                        <div class="result-value">26/05/2025</div>
                    </div>
                    <div>
                        <div style="color: #666; font-size: 14px;">Nepali Date (BS)</div>
                        <div class="result-value" style="color: #27AE60;">2082/02/11</div>
                    </div>
                </div>
            </div>

            <!-- Age Calculator Tab -->
            <div id="calculator-tab" class="tab-content hidden">
                <div class="card animate-in">
                    <div class="card-header">
                        <div class="card-icon">🎂</div>
                        <div>
                            <div class="card-title">Age Calculator</div>
                            <div class="card-subtitle">Calculate your exact age and time lived</div>
                        </div>
                    </div>
                </div>

                <div class="card animate-in">
                    <div class="date-picker" onclick="showBirthDatePicker()">
                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                            <span style="font-size: 20px; margin-right: 8px;">🎂</span>
                            <strong>Select Your Birth Date</strong>
                        </div>
                        <div class="date-value" id="birth-date">15/08/1995</div>
                        <div class="date-details">Tuesday, August 15, 1995</div>
                    </div>
                </div>

                <div class="result-card animate-in">
                    <div class="result-title">👤 Your Age</div>
                    <div class="result-value" id="age-display">29 years, 9 months, 11 days</div>
                    <div style="color: #666; margin-top: 8px;">Your current age</div>
                </div>

                <div class="card animate-in">
                    <div class="result-title">⏰ Time You've Lived</div>
                    <div class="age-stats">
                        <div class="stat-card">
                            <div class="stat-value">357</div>
                            <div class="stat-label">Months</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">1,551</div>
                            <div class="stat-label">Weeks</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">10,857</div>
                            <div class="stat-label">Days</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">260K</div>
                            <div class="stat-label">Hours</div>
                        </div>
                    </div>
                </div>

                <div class="result-card animate-in">
                    <div class="result-title">🎉 Next Birthday</div>
                    <div class="result-value" style="color: #27AE60;">3 months, 20 days</div>
                    <div style="color: #666; margin-top: 8px;">until your next birthday</div>
                </div>
            </div>
        </div>

        <div class="bottom-nav">
            <div class="nav-item active" onclick="switchTab('converter')">
                <div class="nav-icon">📅</div>
                <div class="nav-label">Date Converter</div>
            </div>
            <div class="nav-item" onclick="switchTab('calculator')">
                <div class="nav-icon">🎂</div>
                <div class="nav-label">Age Calculator</div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tab) {
            // Hide all tabs
            document.getElementById('converter-tab').classList.add('hidden');
            document.getElementById('calculator-tab').classList.add('hidden');

            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Show selected tab and activate nav item
            if (tab === 'converter') {
                document.getElementById('converter-tab').classList.remove('hidden');
                document.querySelectorAll('.nav-item')[0].classList.add('active');
            } else {
                document.getElementById('calculator-tab').classList.remove('hidden');
                document.querySelectorAll('.nav-item')[1].classList.add('active');
            }
        }

        function toggleTheme() {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        }

        function showDatePicker(type) {
            const today = new Date();
            const dateStr = prompt(`Enter ${type} date (DD/MM/YYYY):`,
                `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`);

            if (dateStr) {
                const [day, month, year] = dateStr.split('/');
                if (type === 'english') {
                    document.getElementById('english-date').textContent = dateStr;
                    // Simple BS conversion (approximate)
                    const bsYear = parseInt(year) + 57;
                    document.getElementById('nepali-date').textContent = `${bsYear}/${month}/${day}`;
                } else {
                    document.getElementById('nepali-date').textContent = dateStr;
                    // Simple AD conversion (approximate)
                    const adYear = parseInt(year) - 57;
                    document.getElementById('english-date').textContent = `${day}/${month}/${adYear}`;
                }
            }
        }

        function showBirthDatePicker() {
            const dateStr = prompt('Enter your birth date (DD/MM/YYYY):', '15/08/1995');
            if (dateStr) {
                document.getElementById('birth-date').textContent = dateStr;

                // Calculate age
                const [day, month, year] = dateStr.split('/').map(Number);
                const birthDate = new Date(year, month - 1, day);
                const today = new Date();

                let age = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();

                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }

                const ageMonths = monthDiff >= 0 ? monthDiff : 12 + monthDiff;
                const ageDays = today.getDate() >= birthDate.getDate() ?
                    today.getDate() - birthDate.getDate() :
                    30 + today.getDate() - birthDate.getDate();

                document.getElementById('age-display').textContent = `${age} years, ${ageMonths} months, ${ageDays} days`;
            }
        }

        // Load saved theme
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
        }

        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate cards on load
            setTimeout(() => {
                document.querySelectorAll('.card').forEach((card, index) => {
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 100);
        });
    </script>
</body>
</html>
