@echo off
title Android Emulator Setup - Nepali Date Converter
color 0A

echo.
echo ========================================
echo    ANDROID EMULATOR SETUP
echo    Nepali Date Converter
echo ========================================
echo.

echo [STEP 1] Checking Flutter and Android setup...
echo.

echo Checking Flutter...
flutter --version
if %errorlevel% neq 0 (
    echo ❌ Flutter not found!
    pause
    exit /b 1
)

echo.
echo Checking Flutter Doctor...
flutter doctor

echo.
echo [STEP 2] Checking available devices...
flutter devices

echo.
echo ========================================
echo    SETUP OPTIONS
echo ========================================
echo.
echo [1] Download and Install Android Studio (Recommended)
echo [2] Try to run on existing emulator/device
echo [3] Create emulator using command line
echo [4] Build APK for manual installation
echo [5] Run on web (mobile browser)
echo [6] Exit
echo.
set /p choice="Choose option (1-6): "

if "%choice%"=="1" goto install_studio
if "%choice%"=="2" goto run_existing
if "%choice%"=="3" goto create_emulator
if "%choice%"=="4" goto build_apk
if "%choice%"=="5" goto web_mobile
if "%choice%"=="6" goto exit

:install_studio
echo.
echo ========================================
echo    ANDROID STUDIO INSTALLATION
echo ========================================
echo.
echo 1. Download Android Studio from:
echo    https://developer.android.com/studio
echo.
echo 2. Install with these components:
echo    ✅ Android SDK
echo    ✅ Android SDK Platform-Tools
echo    ✅ Android Emulator
echo    ✅ Intel x86 Emulator Accelerator (HAXM)
echo.
echo 3. After installation:
echo    - Open Android Studio
echo    - Go to Tools → AVD Manager
echo    - Create Virtual Device
echo    - Choose Pixel 7 or similar
echo    - Select Android 13 (API 33)
echo    - Start the emulator
echo.
echo 4. Then run this script again and choose option 2
echo.
start https://developer.android.com/studio
pause
goto end

:run_existing
echo.
echo ========================================
echo    RUNNING ON ANDROID
echo ========================================
echo.
echo Checking for devices...
flutter devices

echo.
echo Starting app on Android...
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"

echo.
echo This will:
echo ✅ Build your app for Android
echo ✅ Install on emulator/device
echo ✅ Launch the app
echo.
echo Please wait, this may take 5-10 minutes...
echo.

flutter run

pause
goto end

:create_emulator
echo.
echo ========================================
echo    CREATE EMULATOR (COMMAND LINE)
echo ========================================
echo.
echo This requires Android SDK to be installed.
echo.
echo Commands to create emulator:
echo.
echo 1. List available system images:
echo    sdkmanager --list ^| findstr system-images
echo.
echo 2. Download system image:
echo    sdkmanager "system-images;android-33;google_apis;x86_64"
echo.
echo 3. Create AVD:
echo    avdmanager create avd -n MyEmulator -k "system-images;android-33;google_apis;x86_64"
echo.
echo 4. Start emulator:
echo    emulator -avd MyEmulator
echo.
echo 5. Run app:
echo    flutter run
echo.
echo Note: This requires Android SDK to be installed first.
echo.
pause
goto end

:build_apk
echo.
echo ========================================
echo    BUILDING APK
echo ========================================
echo.
echo Building APK that you can install on any Android device...
echo.
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"

echo Cleaning project...
flutter clean

echo Getting dependencies...
flutter pub get

echo Building APK...
flutter build apk --debug

if %errorlevel%==0 (
    echo.
    echo ✅ APK built successfully!
    echo.
    echo Location: build\app\outputs\flutter-apk\app-debug.apk
    echo.
    echo To install:
    echo 1. Copy APK to your Android device
    echo 2. Enable "Install from Unknown Sources"
    echo 3. Tap APK to install
    echo.
    explorer "build\app\outputs\flutter-apk\"
) else (
    echo ❌ APK build failed. Install Android Studio first.
)

pause
goto end

:web_mobile
echo.
echo ========================================
echo    WEB VERSION FOR MOBILE
echo ========================================
echo.
echo Starting web server for mobile browser access...
echo.
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"

echo.
echo Your app will be available at:
echo http://localhost:8080
echo.
echo For mobile access:
echo 1. Find your computer's IP: ipconfig
echo 2. Open mobile browser
echo 3. Go to: http://YOUR_IP:8080
echo.

flutter run -d web-server --web-port=8080 --web-hostname=0.0.0.0

pause
goto end

:exit
echo.
echo Goodbye!
goto end

:end
