import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nepali_utils/nepali_utils.dart';
import '../widgets/date_picker_card.dart';
import '../widgets/result_card.dart';

class DateConverterScreen extends StatefulWidget {
  const DateConverterScreen({super.key});

  @override
  State<DateConverterScreen> createState() => _DateConverterScreenState();
}

class _DateConverterScreenState extends State<DateConverterScreen>
    with TickerProviderStateMixin {
  DateTime _selectedEnglishDate = DateTime.now();
  NepaliDateTime _selectedNepaliDate = NepaliDateTime.now();
  bool _isUpdatingFromEnglish = false;
  bool _isUpdatingFromNepali = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onEnglishDateChanged(DateTime date) {
    if (_isUpdatingFromNepali) return;
    
    setState(() {
      _isUpdatingFromEnglish = true;
      _selectedEnglishDate = date;
      _selectedNepaliDate = date.toNepaliDateTime();
      _isUpdatingFromEnglish = false;
    });
  }

  void _onNepaliDateChanged(NepaliDateTime date) {
    if (_isUpdatingFromEnglish) return;
    
    setState(() {
      _isUpdatingFromNepali = true;
      _selectedNepaliDate = date;
      _selectedEnglishDate = date.toDateTime();
      _isUpdatingFromNepali = false;
    });
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Copied to clipboard: $text'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    Icon(
                      Icons.swap_horiz,
                      size: 48,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Date Converter',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Convert between Nepali (BS) and English (AD) dates',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Date Pickers
            Expanded(
              child: Column(
                children: [
                  // English Date Picker
                  DatePickerCard(
                    title: 'English Date (AD)',
                    date: _selectedEnglishDate,
                    onDateChanged: _onEnglishDateChanged,
                    isNepali: false,
                  ),
                  const SizedBox(height: 16),

                  // Conversion Arrow
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.swap_vert,
                      color: Theme.of(context).colorScheme.primary,
                      size: 32,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Nepali Date Picker
                  DatePickerCard(
                    title: 'Nepali Date (BS)',
                    nepaliDate: _selectedNepaliDate,
                    onNepaliDateChanged: _onNepaliDateChanged,
                    isNepali: true,
                  ),
                  const SizedBox(height: 20),

                  // Results
                  ResultCard(
                    englishDate: _selectedEnglishDate,
                    nepaliDate: _selectedNepaliDate,
                    onCopy: _copyToClipboard,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
