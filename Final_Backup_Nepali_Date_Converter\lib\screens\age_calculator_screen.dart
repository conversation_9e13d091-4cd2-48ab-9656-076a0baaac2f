import 'package:flutter/material.dart';
import 'package:nepali_utils/nepali_utils.dart';
import '../widgets/age_result_card.dart';
import '../widgets/birthday_countdown_card.dart';
import '../models/age_calculation.dart';
import '../utils/age_calculator.dart';

class AgeCalculatorScreen extends StatefulWidget {
  const AgeCalculatorScreen({super.key});

  @override
  State<AgeCalculatorScreen> createState() => _AgeCalculatorScreenState();
}

class _AgeCalculatorScreenState extends State<AgeCalculatorScreen>
    with TickerProviderStateMixin {
  DateTime? _selectedBirthDate;
  NepaliDateTime? _selectedNepaliBirthDate;
  AgeCalculation? _ageCalculation;
  bool _useNepaliDate = false;

  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _selectDate() async {
    if (_useNepaliDate) {
      final nepaliDate = await showDialog<NepaliDateTime>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Select Birth Date (BS)'),
          content: SizedBox(
            height: 300,
            width: 300,
            child: Column(
              children: [
                Text('Select Nepali Date'),
                // Simple date input for now - can be enhanced with a proper Nepali date picker
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(NepaliDateTime.now());
                  },
                  child: Text('Select Today'),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel'),
            ),
          ],
        ),
      );

      if (nepaliDate != null) {
        setState(() {
          _selectedNepaliBirthDate = nepaliDate;
          _selectedBirthDate = nepaliDate.toDateTime();
          _calculateAge();
        });
      }
    } else {
      final date = await showDatePicker(
        context: context,
        initialDate: _selectedBirthDate ?? DateTime.now(),
        firstDate: DateTime(1900),
        lastDate: DateTime.now(),
      );

      if (date != null) {
        setState(() {
          _selectedBirthDate = date;
          _selectedNepaliBirthDate = date.toNepaliDateTime();
          _calculateAge();
        });
      }
    }
  }

  void _calculateAge() {
    if (_selectedBirthDate != null) {
      setState(() {
        _ageCalculation = AgeCalculatorUtil.calculateAge(_selectedBirthDate!);
      });
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                children: [
                  Icon(
                    Icons.cake,
                    size: 48,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Age Calculator',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Calculate your exact age and time lived',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Date Type Toggle
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Use Nepali Date (BS)',
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ),
                  Switch(
                    value: _useNepaliDate,
                    onChanged: (value) {
                      setState(() {
                        _useNepaliDate = value;
                        _selectedBirthDate = null;
                        _selectedNepaliBirthDate = null;
                        _ageCalculation = null;
                      });
                      _animationController.reset();
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Date Selection
          Card(
            child: InkWell(
              onTap: _selectDate,
              borderRadius: BorderRadius.circular(16),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 32,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      _selectedBirthDate == null
                          ? 'Select Your Birth Date'
                          : _useNepaliDate
                              ? 'Birth Date: ${_selectedNepaliBirthDate!.format('yyyy/MM/dd')}'
                              : 'Birth Date: ${_selectedBirthDate!.day}/${_selectedBirthDate!.month}/${_selectedBirthDate!.year}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                      textAlign: TextAlign.center,
                    ),
                    if (_selectedBirthDate == null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Tap to select date',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Results
          if (_ageCalculation != null) ...[
            Expanded(
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 1),
                  end: Offset.zero,
                ).animate(_slideAnimation),
                child: Column(
                  children: [
                    AgeResultCard(ageCalculation: _ageCalculation!),
                    const SizedBox(height: 16),
                    BirthdayCountdownCard(
                      birthDate: _selectedBirthDate!,
                      useNepaliDate: _useNepaliDate,
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Select your birth date to see your age calculation',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Colors.grey[600],
                          ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
