@echo off
title Nepali Date Converter - Quick Start
color 0A

echo.
echo ========================================
echo    NEPALI DATE CONVERTER
echo    Quick Start Menu
echo ========================================
echo.
echo Choose an option:
echo.
echo [1] Open HTML Preview (Instant)
echo [2] Run Flutter App (Full Features)
echo [3] View Documentation
echo [4] Exit
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto html_preview
if "%choice%"=="2" goto flutter_app
if "%choice%"=="3" goto documentation
if "%choice%"=="4" goto exit
goto invalid

:html_preview
echo.
echo Opening HTML Preview...
start "" "preview.html"
echo ✅ HTML Preview opened in your browser!
echo.
echo Features available:
echo - Interactive date conversion
echo - Age calculator
echo - Dark/light mode toggle
echo - Responsive design
echo.
pause
goto menu

:flutter_app
echo.
echo Starting Flutter App...
echo This may take a few minutes on first run...
echo.
set FLUTTER_BIN=C:\Users\<USER>\Downloads\flutter_windows_3.32.0-stable\flutter\bin
"%FLUTTER_BIN%\flutter.bat" run -d web-server --web-port=8080
pause
goto menu

:documentation
echo.
echo Opening Documentation...
start "" "README.md"
start "" "FEATURES_DOCUMENTATION.md"
start "" "INSTALLATION_GUIDE.md"
echo ✅ Documentation files opened!
pause
goto menu

:invalid
echo.
echo ❌ Invalid choice. Please enter 1, 2, 3, or 4.
echo.
pause
goto menu

:exit
echo.
echo Thank you for using Nepali Date Converter!
echo.
exit

:menu
cls
goto start
