#!/usr/bin/env python3
"""
Simple HTTP server to serve the Flutter app preview
"""
import http.server
import socketserver
import webbrowser
import os
import sys

PORT = 8080

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

def main():
    # Change to the project directory
    project_dir = r"C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
    os.chdir(project_dir)
    
    print("🚀 Starting Nepali Date Converter Preview Server...")
    print(f"📁 Serving from: {project_dir}")
    print(f"🌐 Server running at: http://localhost:{PORT}")
    print("📱 Opening preview.html in your browser...")
    print("⏹️  Press Ctrl+C to stop the server")
    print("=" * 50)
    
    # Start the server
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        # Open the browser
        webbrowser.open(f'http://localhost:{PORT}/preview.html')
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped by user")
            sys.exit(0)

if __name__ == "__main__":
    main()
