# Flutter App Runner Script
Write-Host "🚀 Starting Nepali Date Converter App..." -ForegroundColor Green

# Set Flutter path
$flutterPath = "C:\Users\<USER>\Downloads\flutter_windows_3.32.0-stable\flutter\bin"
$env:PATH = "$env:PATH;$flutterPath"

# Navigate to project directory
Set-Location "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"

Write-Host "📋 Checking Flutter installation..." -ForegroundColor Yellow
& "$flutterPath\flutter.bat" --version

Write-Host "📦 Getting dependencies..." -ForegroundColor Yellow
& "$flutterPath\flutter.bat" pub get

Write-Host "🌐 Enabling web support..." -ForegroundColor Yellow
& "$flutterPath\flutter.bat" config --enable-web

Write-Host "🎯 Running app in web mode..." -ForegroundColor Green
Write-Host "App will be available at: http://localhost:8080" -ForegroundColor Cyan
& "$flutterPath\flutter.bat" run -d web-server --web-port=8080

Write-Host "✅ App is running! Press Ctrl+C to stop." -ForegroundColor Green
