import '../models/age_calculation.dart';

class AgeCalculatorUtil {
  static AgeCalculation calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    
    // Calculate years, months, days
    int years = now.year - birthDate.year;
    int months = now.month - birthDate.month;
    int days = now.day - birthDate.day;

    // Adjust for negative days
    if (days < 0) {
      months--;
      final lastMonth = DateTime(now.year, now.month, 0);
      days += lastMonth.day;
    }

    // Adjust for negative months
    if (months < 0) {
      years--;
      months += 12;
    }

    // Calculate total values
    final difference = now.difference(birthDate);
    final totalDays = difference.inDays;
    final totalWeeks = (totalDays / 7).floor();
    final totalMonths = (years * 12) + months;
    final totalHours = difference.inHours;
    final totalMinutes = difference.inMinutes;
    final totalSeconds = difference.inSeconds;

    return AgeCalculation(
      years: years,
      months: months,
      days: days,
      totalMonths: totalMonths,
      totalWeeks: totalWeeks,
      totalDays: totalDays,
      totalHours: totalHours,
      totalMinutes: totalMinutes,
      totalSeconds: totalSeconds,
    );
  }

  static DateTime getNextBirthday(DateTime birthDate) {
    final now = DateTime.now();
    DateTime nextBirthday = DateTime(now.year, birthDate.month, birthDate.day);
    
    if (nextBirthday.isBefore(now) || nextBirthday.isAtSameMomentAs(now)) {
      nextBirthday = DateTime(now.year + 1, birthDate.month, birthDate.day);
    }
    
    return nextBirthday;
  }

  static Duration getTimeUntilNextBirthday(DateTime birthDate) {
    final nextBirthday = getNextBirthday(birthDate);
    return nextBirthday.difference(DateTime.now());
  }

  static String formatDuration(Duration duration) {
    final days = duration.inDays;
    final hours = duration.inHours % 24;
    final minutes = duration.inMinutes % 60;
    
    if (days > 0) {
      return '$days days, $hours hours, $minutes minutes';
    } else if (hours > 0) {
      return '$hours hours, $minutes minutes';
    } else {
      return '$minutes minutes';
    }
  }
}
