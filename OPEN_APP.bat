@echo off
title Open Nepali Date Converter
color 0A

echo.
echo ========================================
echo    NEPALI DATE CONVERTER
echo    Choose Your Option
echo ========================================
echo.
echo [1] Open HTML Preview (Instant - Recommended)
echo [2] Try Flutter App (May take time)
echo [3] Open Both
echo [4] Exit
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto html_only
if "%choice%"=="2" goto flutter_only
if "%choice%"=="3" goto both
if "%choice%"=="4" goto exit

:html_only
echo.
echo ✅ Opening HTML Preview...
start "" "preview.html"
echo.
echo ========================================
echo  HTML Preview Features:
echo  ✅ Date Converter (AD ↔️ BS)
echo  ✅ Age Calculator
echo  ✅ Dark/Light Mode Toggle
echo  ✅ Responsive Design
echo  ✅ Full Functionality
echo ========================================
echo.
echo Your app is now open in the browser!
pause
goto end

:flutter_only
echo.
echo ⏳ Starting Flutter App...
echo This may take several minutes on first run...
echo.
echo If it takes too long, press Ctrl+C and use HTML preview instead.
echo.
flutter run -d web-server --web-port=8080
pause
goto end

:both
echo.
echo ✅ Opening HTML Preview...
start "" "preview.html"
echo.
echo ⏳ Starting Flutter App...
echo.
start cmd /k "flutter run -d web-server --web-port=8080"
echo.
echo ========================================
echo  Both versions are starting:
echo  ✅ HTML Preview: Instant access
echo  ⏳ Flutter App: http://localhost:8080
echo ========================================
pause
goto end

:exit
echo.
echo Goodbye!
goto end

:end
