import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nepali_utils/nepali_utils.dart';

class DateConverterWidget extends StatefulWidget {
  const DateConverterWidget({super.key});

  @override
  State<DateConverterWidget> createState() => _DateConverterWidgetState();
}

class _DateConverterWidgetState extends State<DateConverterWidget> {
  bool _isBsToAd = true;
  final TextEditingController _yearController = TextEditingController();
  final TextEditingController _monthController = TextEditingController();
  final TextEditingController _dayController = TextEditingController();
  String? _convertedDate;
  String? _errorMessage;

  @override
  void dispose() {
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    super.dispose();
  }

  void _clearDate() {
    setState(() {
      _yearController.clear();
      _monthController.clear();
      _dayController.clear();
      _convertedDate = null;
      _errorMessage = null;
    });
  }

  void _copyToClipboard(String text) async {
    await Clipboard.setData(ClipboardData(text: text));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Copied: $text'),
          duration: const Duration(seconds: 2),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _convert() {
    setState(() {
      _errorMessage = null;
      _convertedDate = null;
    });

    try {
      final year = int.parse(_yearController.text);
      final month = int.parse(_monthController.text);
      final day = int.parse(_dayController.text);

      if (_isBsToAd) {
        // BS to AD conversion
        if (year < 1970 || year > 2100) {
          setState(() {
            _errorMessage = 'Year must be between 1970 and 2100';
          });
          return;
        }
        if (month < 1 || month > 12) {
          setState(() {
            _errorMessage = 'Month must be between 1 and 12';
          });
          return;
        }
        if (day < 1 || day > 32) {
          setState(() {
            _errorMessage = 'Day must be between 1 and 32';
          });
          return;
        }

        try {
          final nepaliDate = NepaliDateTime(year, month, day);
          final adDate = nepaliDate.toDateTime();
          setState(() {
            _convertedDate =
                '${adDate.year}/${adDate.month.toString().padLeft(2, '0')}/${adDate.day.toString().padLeft(2, '0')} (AD)';
          });
        } catch (e) {
          setState(() {
            _errorMessage = 'Invalid Nepali date. Please check the date.';
          });
        }
      } else {
        // AD to BS conversion
        if (year < 1913 || year > 2043) {
          setState(() {
            _errorMessage = 'Year must be between 1913 and 2043';
          });
          return;
        }
        if (month < 1 || month > 12) {
          setState(() {
            _errorMessage = 'Month must be between 1 and 12';
          });
          return;
        }
        if (day < 1 || day > 31) {
          setState(() {
            _errorMessage = 'Day must be between 1 and 31';
          });
          return;
        }

        try {
          final adDate = DateTime(year, month, day);
          final nepaliDate = adDate.toNepaliDateTime();
          setState(() {
            _convertedDate =
                '${nepaliDate.year}/${nepaliDate.month.toString().padLeft(2, '0')}/${nepaliDate.day.toString().padLeft(2, '0')} (BS)';
          });
        } catch (e) {
          setState(() {
            _errorMessage = 'Invalid English date. Please check the date.';
          });
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid date format. Please enter valid numbers.';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tab selector
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(25),
          ),
          child: Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => setState(() => _isBsToAd = true),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: _isBsToAd
                          ? Colors.red.withValues(alpha: 0.5)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Text(
                      'BS TO AD',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: _isBsToAd ? Colors.white : Colors.black54,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () => setState(() => _isBsToAd = false),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: !_isBsToAd
                          ? Colors.red.withValues(alpha: 0.5)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Text(
                      'AD TO BS',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: !_isBsToAd ? Colors.white : Colors.black54,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Input fields
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _yearController,
                decoration: const InputDecoration(
                  labelText: 'Year',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _monthController,
                decoration: const InputDecoration(
                  labelText: 'Month',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _dayController,
                decoration: const InputDecoration(
                  labelText: 'Day',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Convert button (fixed height)
        SizedBox(
          width: double.infinity,
          height: 44,
          child: ElevatedButton(
            onPressed: _convert,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'CONVERT',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Clear button
        TextButton(
          onPressed: _clearDate,
          child: Text(
            'Clear Date',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Result or Error Display
        if (_errorMessage != null)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.error, color: Colors.red, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red, fontSize: 14),
                  ),
                ),
              ],
            ),
          )
        else if (_convertedDate != null)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.green, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Result: $_convertedDate',
                    style: const TextStyle(
                      color: Colors.green,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _copyToClipboard(_convertedDate!),
                  icon: const Icon(
                    Icons.copy,
                    color: Colors.green,
                    size: 18,
                  ),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                  tooltip: 'Copy result',
                ),
              ],
            ),
          ),
      ],
    );
  }
}
