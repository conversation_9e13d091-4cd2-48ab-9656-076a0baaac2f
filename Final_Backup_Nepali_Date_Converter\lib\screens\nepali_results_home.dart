import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/app_menu.dart';

class NepaliResultsHome extends StatelessWidget {
  const NepaliResultsHome({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF87CEEB), // Sky blue
              Color(0xFFE6F3FF), // Light blue
              Color(0xFFF0F8FF), // Alice blue
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // TOP BAR WITH DATE AND CONTROLS
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    // Calendar Icon and Date
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.9),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            color: Color(0xFF4A90E2),
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'शनिबार, २०८२-०१-२८',
                            style: TextStyle(
                              color: Colors.grey[800],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    
                    // Notification Badge
                    Container(
                      width: 24,
                      height: 24,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: const Center(
                        child: Text(
                          '3',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    
                    // Dark Mode Toggle
                    Consumer<ThemeProvider>(
                      builder: (context, themeProvider, child) {
                        return GestureDetector(
                          onTap: () => themeProvider.toggleTheme(),
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.9),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                              color: const Color(0xFF4A90E2),
                              size: 20,
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(width: 12),
                    
                    // Menu
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.9),
                        shape: BoxShape.circle,
                      ),
                      child: const AppMenu(),
                    ),
                  ],
                ),
              ),
              
              // MAIN TITLE
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  children: [
                    Text(
                      'Nepali Results',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2E5BBA),
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Check your results easily',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF6B9BD1),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 40),
              
              // RESULTS CARDS GRID
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    children: [
                      // First Row
                      Row(
                        children: [
                          Expanded(
                            child: _buildResultCard(
                              'SEE Results',
                              'SEE',
                              const Color(0xFF4A90E2),
                              const Color(0xFFE3F2FD),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildResultCard(
                              'HSEB Results',
                              'HSEB',
                              const Color(0xFFE53E3E),
                              const Color(0xFFFFEBEE),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Second Row
                      Row(
                        children: [
                          Expanded(
                            child: _buildResultCard(
                              'TU Results',
                              'TU',
                              const Color(0xFFFF9500),
                              const Color(0xFFFFF3E0),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildResultCard(
                              'KU Results',
                              'KU',
                              const Color(0xFF00C851),
                              const Color(0xFFE8F5E8),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Third Row (Partial)
                      Row(
                        children: [
                          Expanded(
                            child: _buildResultCard(
                              'PU Results',
                              'PU',
                              const Color(0xFF9C27B0),
                              const Color(0xFFF3E5F5),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildResultCard(
                              'Other Results',
                              'OTHER',
                              const Color(0xFF607D8B),
                              const Color(0xFFECEFF1),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              // BOTTOM NAVIGATION
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildNavItem(Icons.home, 'Home', true),
                    _buildNavItem(Icons.bookmark_border, '', false),
                    _buildNavItem(Icons.share, '', false),
                    _buildNavItem(Icons.settings, '', false),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildResultCard(String title, String logoText, Color iconColor, Color backgroundColor) {
    return Container(
      height: 140,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                logoText,
                style: TextStyle(
                  color: iconColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2D3436),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildNavItem(IconData icon, String label, bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isActive ? const Color(0xFF4A90E2) : Colors.transparent,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: isActive ? Colors.white : Colors.grey[600],
            size: 20,
          ),
          if (label.isNotEmpty) ...[
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isActive ? Colors.white : Colors.grey[600],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
