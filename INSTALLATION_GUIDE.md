# 🚀 Installation & Setup Guide

## Quick Start (Recommended)

### Option 1: HTML Preview (Works Immediately)
1. Navigate to: `C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter`
2. Double-click `preview.html`
3. ✅ **App opens in browser with full functionality**

### Option 2: Flutter App (Full Features)
1. **Open Command Prompt**
2. **Run these commands:**

```bash
cd "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
```

```bash
"C:\Users\<USER>\Downloads\flutter_windows_3.32.0-stable\flutter\bin\flutter.bat" pub get
```

```bash
"C:\Users\<USER>\Downloads\flutter_windows_3.32.0-stable\flutter\bin\flutter.bat" run -d web-server --web-port=8080
```

3. **Open browser to:** `http://localhost:8080`

## Detailed Flutter Setup

### Prerequisites
- ✅ Flutter SDK (already downloaded in your Downloads folder)
- ✅ Web browser (Chrome, Edge, Firefox)
- ✅ Command Prompt access

### Step-by-Step Installation

#### 1. Add Flutter to PATH (Optional but Recommended)
1. Press `Win + R`, type `sysdm.cpl`, press Enter
2. Click **Advanced** → **Environment Variables**
3. Under **System Variables**, find and edit **Path**
4. Add: `C:\Users\<USER>\Downloads\flutter_windows_3.32.0-stable\flutter\bin`
5. Click **OK** and restart Command Prompt

#### 2. Verify Flutter Installation
```bash
flutter --version
flutter doctor
```

#### 3. Navigate to Project
```bash
cd "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
```

#### 4. Install Dependencies
```bash
flutter pub get
```

#### 5. Run the App
```bash
flutter run -d web-server --web-port=8080
```

## Troubleshooting

### Common Issues & Solutions

#### Issue: "Flutter not recognized"
**Solution:**
```bash
"C:\Users\<USER>\Downloads\flutter_windows_3.32.0-stable\flutter\bin\flutter.bat" --version
```

#### Issue: "Web support not enabled"
**Solution:**
```bash
flutter config --enable-web
```

#### Issue: "Dependencies error"
**Solution:**
```bash
flutter clean
flutter pub get
```

#### Issue: "Build failed"
**Solution:**
```bash
flutter clean
flutter pub get
flutter run -d web-server --web-port=8080
```

### Alternative Methods

#### Method 1: Use Batch File
1. Double-click `run_simple_app.bat`
2. Wait for compilation
3. Open `http://localhost:8080`

#### Method 2: Use PowerShell Script
1. Right-click `debug_flutter.ps1`
2. Select "Run with PowerShell"
3. Follow on-screen instructions

## Features to Test

### In HTML Preview
- ✅ Click date fields to enter dates
- ✅ Test date conversion (AD ↔️ BS)
- ✅ Calculate age with birth date
- ✅ Toggle dark/light mode
- ✅ Switch between tabs

### In Flutter App
- ✅ Interactive date pickers
- ✅ Real-time conversion
- ✅ Smooth animations
- ✅ Responsive design
- ✅ Theme persistence

## Performance Tips

### For Better Performance
1. **Close other applications** while running Flutter
2. **Use Chrome browser** for best web performance
3. **Clear browser cache** if issues occur
4. **Restart Command Prompt** if commands fail

### System Requirements
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **OS**: Windows 10/11
- **Browser**: Chrome, Edge, or Firefox

## Next Steps

### After Installation
1. **Test all features** in both HTML and Flutter versions
2. **Customize colors** in `lib/main.dart` if desired
3. **Add app icon** in `assets/icon/` folder
4. **Build APK** for Android: `flutter build apk`
5. **Deploy to web** hosting if needed

### Development
1. **Edit code** in VS Code or Android Studio
2. **Hot reload** with `r` in Flutter terminal
3. **Add features** as needed
4. **Test on mobile** devices

---

**✅ Your app is ready to use!**  
**Choose HTML preview for immediate access or Flutter for full features.**
