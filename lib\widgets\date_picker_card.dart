import 'package:flutter/material.dart';
import 'package:nepali_utils/nepali_utils.dart';

class DatePickerCard extends StatelessWidget {
  final String title;
  final DateTime? date;
  final NepaliDateTime? nepaliDate;
  final Function(DateTime)? onDateChanged;
  final Function(NepaliDateTime)? onNepaliDateChanged;
  final bool isNepali;

  const DatePickerCard({
    super.key,
    required this.title,
    this.date,
    this.nepaliDate,
    this.onDateChanged,
    this.onNepaliDateChanged,
    required this.isNepali,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: () => _selectDate(context),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    isNepali ? Icons.calendar_month : Icons.calendar_today,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getFormattedDate(),
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getDateDetails(),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.touch_app,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Tap to change date',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getFormattedDate() {
    if (isNepali && nepaliDate != null) {
      return nepaliDate!.format('yyyy/MM/dd');
    } else if (!isNepali && date != null) {
      return '${date!.day}/${date!.month}/${date!.year}';
    }
    return 'Select Date';
  }

  String _getDateDetails() {
    if (isNepali && nepaliDate != null) {
      return '${nepaliDate!.format('EEEE')}, ${nepaliDate!.format('MMMM')} ${nepaliDate!.day}, ${nepaliDate!.year}';
    } else if (!isNepali && date != null) {
      final weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
      final months = ['January', 'February', 'March', 'April', 'May', 'June',
                     'July', 'August', 'September', 'October', 'November', 'December'];
      return '${weekdays[date!.weekday - 1]}, ${months[date!.month - 1]} ${date!.day}, ${date!.year}';
    }
    return 'No date selected';
  }

  void _selectDate(BuildContext context) async {
    if (isNepali) {
      final selectedDate = await showDialog<NepaliDateTime>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Select $title'),
          content: SizedBox(
            height: 200,
            width: 300,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Nepali Date Picker'),
                SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(NepaliDateTime.now());
                  },
                  child: Text('Select Today (BS)'),
                ),
                SizedBox(height: 10),
                Text('${NepaliDateTime.now().format('yyyy/MM/dd')}'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel'),
            ),
          ],
        ),
      );

      if (selectedDate != null && onNepaliDateChanged != null) {
        onNepaliDateChanged!(selectedDate);
      }
    } else {
      final selectedDate = await showDatePicker(
        context: context,
        initialDate: date ?? DateTime.now(),
        firstDate: DateTime(1900),
        lastDate: DateTime(2100),
      );

      if (selectedDate != null && onDateChanged != null) {
        onDateChanged!(selectedDate);
      }
    }
  }
}
