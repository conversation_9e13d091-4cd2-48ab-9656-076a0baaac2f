@echo off
title Build Android APK - Nepali Date Converter
color 0A

echo.
echo ========================================
echo    BUILD ANDROID APK
echo    Nepali Date Converter
echo ========================================
echo.

echo [1/5] Checking Flutter setup...
flutter --version >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Flutter found!
) else (
    echo ❌ Flutter not found in PATH
    echo Please add Flutter to your PATH first
    pause
    exit /b 1
)

echo.
echo [2/5] Navigating to project directory...
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
echo Current directory: %CD%

echo.
echo [3/5] Cleaning previous builds...
flutter clean

echo.
echo [4/5] Getting dependencies...
flutter pub get

echo.
echo [5/5] Building Android APK...
echo This may take 5-10 minutes...
echo.
echo ========================================
echo  Building APK for Android...
echo  Please wait...
echo ========================================
echo.

flutter build apk --debug

if %errorlevel%==0 (
    echo.
    echo ========================================
    echo  ✅ APK BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo APK Location:
    echo %CD%\build\app\outputs\flutter-apk\app-debug.apk
    echo.
    echo File size: 
    dir "build\app\outputs\flutter-apk\app-debug.apk" | find "app-debug.apk"
    echo.
    echo ========================================
    echo  How to install on Android device:
    echo ========================================
    echo.
    echo 1. Copy APK to your Android device
    echo 2. Enable "Install from Unknown Sources"
    echo 3. Tap the APK file to install
    echo 4. Open "Nepali Date Converter" app
    echo.
    echo ========================================
    echo  Want to open APK location?
    echo ========================================
    echo.
    set /p open="Open APK folder? (y/n): "
    if /i "%open%"=="y" (
        explorer "build\app\outputs\flutter-apk\"
    )
) else (
    echo.
    echo ❌ APK build failed!
    echo.
    echo Common solutions:
    echo 1. Install Android Studio
    echo 2. Accept Android licenses: flutter doctor --android-licenses
    echo 3. Check flutter doctor: flutter doctor
    echo.
)

echo.
pause
