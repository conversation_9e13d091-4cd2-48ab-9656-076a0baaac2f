@echo off
title Run Nepali Date Converter on Android
color 0A

echo.
echo ========================================
echo    NEPALI DATE CONVERTER
echo    Android Emulator Options
echo ========================================
echo.

echo Choose your option:
echo.
echo [1] Check for Android devices/emulators
echo [2] Run on Android (if device available)
echo [3] Build APK for manual installation
echo [4] Start web server for mobile browser
echo [5] Download Android Studio
echo [6] Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto check_devices
if "%choice%"=="2" goto run_android
if "%choice%"=="3" goto build_apk
if "%choice%"=="4" goto web_server
if "%choice%"=="5" goto download_studio
if "%choice%"=="6" goto exit

:check_devices
echo.
echo Checking available devices...
flutter devices
echo.
echo If you see "No devices found", you need to:
echo 1. Start Android emulator, OR
echo 2. Connect Android phone with USB debugging, OR
echo 3. Install Android Studio first
echo.
pause
goto menu

:run_android
echo.
echo Running on Android device/emulator...
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
echo.
echo This will build and install your app.
echo Please wait 5-10 minutes for first run...
echo.
flutter run
pause
goto menu

:build_apk
echo.
echo Building APK file...
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
echo.
echo Building debug APK...
flutter build apk --debug
echo.
if exist "build\app\outputs\flutter-apk\app-debug.apk" (
    echo ✅ APK built successfully!
    echo.
    echo Location: build\app\outputs\flutter-apk\app-debug.apk
    echo.
    echo To install on Android:
    echo 1. Copy APK to your phone
    echo 2. Enable "Install from Unknown Sources"
    echo 3. Tap APK file to install
    echo.
    explorer "build\app\outputs\flutter-apk\"
) else (
    echo ❌ APK build failed.
    echo Please install Android Studio first.
)
pause
goto menu

:web_server
echo.
echo Starting web server for mobile access...
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
echo.
echo Starting server on port 8080...
echo.
echo To access from mobile:
echo 1. Find your computer's IP address
echo 2. Open mobile browser
echo 3. Go to: http://YOUR_IP:8080
echo.
echo Press Ctrl+C to stop server
echo.
flutter run -d web-server --web-port=8080 --web-hostname=0.0.0.0
pause
goto menu

:download_studio
echo.
echo Opening Android Studio download page...
start https://developer.android.com/studio
echo.
echo After downloading and installing:
echo 1. Open Android Studio
echo 2. Go to Tools → AVD Manager
echo 3. Create Virtual Device
echo 4. Choose Pixel 7, Android 13
echo 5. Start the emulator
echo 6. Run this script again and choose option 2
echo.
pause
goto menu

:exit
echo.
echo Goodbye!
exit

:menu
cls
echo.
echo ========================================
echo    NEPALI DATE CONVERTER
echo    Android Emulator Options
echo ========================================
echo.

echo Choose your option:
echo.
echo [1] Check for Android devices/emulators
echo [2] Run on Android (if device available)
echo [3] Build APK for manual installation
echo [4] Start web server for mobile browser
echo [5] Download Android Studio
echo [6] Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto check_devices
if "%choice%"=="2" goto run_android
if "%choice%"=="3" goto build_apk
if "%choice%"=="4" goto web_server
if "%choice%"=="5" goto download_studio
if "%choice%"=="6" goto exit
goto menu
