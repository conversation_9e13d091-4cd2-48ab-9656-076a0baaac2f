# Nepali Date Converter & Age Calculator

A modern Flutter mobile application for converting between Nepali (<PERSON><PERSON><PERSON>) and English (Gregorian) dates, and calculating detailed age statistics.

## 🎯 Features

### Date Converter
- **Bidirectional Conversion**: Convert between Nepali (BS) and English (AD) dates
- **Real-time Updates**: Automatic conversion when one date is changed
- **Copy to Clipboard**: Easy sharing of converted dates
- **Accurate Conversion**: Uses the `nepali_utils` package for precise calculations

### Age Calculator
- **Detailed Age Display**: Shows age in years, months, and days
- **Comprehensive Statistics**: Total months, weeks, days, hours, minutes, and seconds lived
- **Birthday Countdown**: Shows time remaining until next birthday
- **Dual Calendar Support**: Calculate age using either BS or AD dates
- **Animated Results**: Beautiful animations for displaying age statistics

### Modern UI/UX
- **Material Design 3**: Clean, modern interface following latest design guidelines
- **Dark Mode Support**: Toggle between light and dark themes with persistent settings
- **Responsive Design**: Works perfectly on phones and tablets
- **Smooth Animations**: Engaging micro-interactions and transitions
- **Futuristic Color Scheme**: Sky blue and mint green color palette

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (3.0.0 or higher)
- Dart SDK
- Android Studio / VS Code
- Android device or emulator / iOS device or simulator

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd nepali_date_converter
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   flutter run
   ```

### Building for Release

#### Android
```bash
flutter build apk --release
```

#### iOS
```bash
flutter build ios --release
```

## 📱 Screenshots

*Add screenshots of your app here*

## 🛠️ Technical Details

### Dependencies
- **nepali_utils**: Nepali date conversion and utilities
- **provider**: State management
- **animated_text_kit**: Text animations
- **google_fonts**: Typography
- **shared_preferences**: Local storage for theme settings

### Architecture
- **Provider Pattern**: For state management
- **Widget-based Architecture**: Modular and reusable components
- **Material Design 3**: Modern UI components and theming

### Key Components
- `DateConverterScreen`: Main date conversion interface
- `AgeCalculatorScreen`: Age calculation and statistics
- `ThemeProvider`: Dark/light mode management
- `AgeCalculatorUtil`: Age calculation logic
- Custom widgets for date pickers, result cards, and statistics

## 🎨 Design System

### Colors
- **Primary**: #2D9CDB (Sky Blue)
- **Secondary**: #27AE60 (Mint Green)
- **Light Background**: #F2F2F2
- **Dark Background**: #121212

### Typography
- **Font Family**: Roboto (Google Fonts)
- **Responsive Text Scaling**: Adapts to device settings

## 🌟 Features in Detail

### Offline Capability
The app works completely offline for all date conversions and age calculations. No internet connection required.

### Responsive Design
- Adapts to different screen sizes
- Optimized for both phones and tablets
- Portrait and landscape orientations supported

### Accessibility
- High contrast colors
- Readable fonts
- Touch-friendly interface elements

## 🔧 Development

### Project Structure
```
lib/
├── main.dart                 # App entry point
├── models/                   # Data models
├── providers/                # State management
├── screens/                  # App screens
├── utils/                    # Utility functions
└── widgets/                  # Reusable widgets
```

### Adding New Features
1. Create new widgets in the `widgets/` directory
2. Add business logic to `utils/` or `providers/`
3. Update screens in the `screens/` directory
4. Follow the existing code style and patterns

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you have any questions or need help, please open an issue on GitHub.

---

**Made with ❤️ using Flutter**
