# Quick Android Emulator Setup for Nepali Date Converter
Write-Host "🚀 Quick Android Emulator Setup" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Check if Android Studio is installed
$androidStudioPaths = @(
    "C:\Program Files\Android\Android Studio",
    "C:\Users\<USER>\AppData\Local\Android\Sdk",
    "C:\Android\Sdk"
)

$androidStudioFound = $false
foreach ($path in $androidStudioPaths) {
    if (Test-Path $path) {
        Write-Host "✅ Android SDK found at: $path" -ForegroundColor Green
        $androidStudioFound = $true
        break
    }
}

if (-not $androidStudioFound) {
    Write-Host "❌ Android Studio/SDK not found" -ForegroundColor Red
    Write-Host ""
    Write-Host "📥 OPTION 1: Download Android Studio" -ForegroundColor Yellow
    Write-Host "1. Go to: https://developer.android.com/studio"
    Write-Host "2. Download Android Studio (1GB+)"
    Write-Host "3. Install with default settings"
    Write-Host "4. Open Android Studio → Tools → AVD Manager"
    Write-Host "5. Create Virtual Device → Pixel 7 → Android 13"
    Write-Host ""
    
    $download = Read-Host "Open Android Studio download page? (y/n)"
    if ($download -eq "y" -or $download -eq "Y") {
        Start-Process "https://developer.android.com/studio"
    }
    
    Write-Host ""
    Write-Host "📱 OPTION 2: Use Your Phone" -ForegroundColor Yellow
    Write-Host "1. Enable Developer Options (tap Build Number 7 times)"
    Write-Host "2. Enable USB Debugging"
    Write-Host "3. Connect phone to computer"
    Write-Host "4. Run: flutter run"
    Write-Host ""
    
    Write-Host "🌐 OPTION 3: Web Version (Works Now)" -ForegroundColor Yellow
    Write-Host "1. Run: flutter run -d web-server --web-port=8080"
    Write-Host "2. Open: http://localhost:8080"
    Write-Host "3. Or use mobile browser with your computer's IP"
    Write-Host ""
    
    $webOption = Read-Host "Start web version now? (y/n)"
    if ($webOption -eq "y" -or $webOption -eq "Y") {
        Set-Location "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
        Write-Host "🌐 Starting web server..." -ForegroundColor Green
        & flutter run -d web-server --web-port=8080 --web-hostname=0.0.0.0
    }
    
    exit
}

# If Android Studio is found, check for emulators
Write-Host "🔍 Checking for available emulators..." -ForegroundColor Yellow

# Check Flutter devices
Write-Host "📱 Available Flutter devices:" -ForegroundColor Cyan
& flutter devices

Write-Host ""
Write-Host "🎯 Choose your option:" -ForegroundColor Yellow
Write-Host "1. Run on existing emulator/device"
Write-Host "2. Create new emulator"
Write-Host "3. Build APK for manual installation"
Write-Host "4. Start web version"

$choice = Read-Host "Enter choice (1-4)"

switch ($choice) {
    "1" {
        Write-Host "🚀 Running app on Android..." -ForegroundColor Green
        Set-Location "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
        & flutter run
    }
    "2" {
        Write-Host "🛠️ Creating new emulator..." -ForegroundColor Green
        Write-Host "This requires Android Studio to be fully set up."
        Write-Host "Please use Android Studio AVD Manager to create emulator."
        Start-Process "studio"
    }
    "3" {
        Write-Host "📦 Building APK..." -ForegroundColor Green
        Set-Location "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
        & flutter build apk --debug
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ APK built successfully!" -ForegroundColor Green
            Write-Host "Location: build\app\outputs\flutter-apk\app-debug.apk"
            Start-Process "explorer" -ArgumentList "build\app\outputs\flutter-apk\"
        }
    }
    "4" {
        Write-Host "🌐 Starting web version..." -ForegroundColor Green
        Set-Location "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
        & flutter run -d web-server --web-port=8080 --web-hostname=0.0.0.0
    }
    default {
        Write-Host "Invalid choice. Exiting." -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "✅ Setup complete!" -ForegroundColor Green
