# Debug Flutter Setup
Write-Host "🔍 Debugging Flutter Setup..." -ForegroundColor Yellow

# Set Flutter path
$flutterPath = "C:\Users\<USER>\Downloads\flutter_windows_3.32.0-stable\flutter\bin"
$flutterExe = "$flutterPath\flutter.bat"

Write-Host "📁 Checking Flutter path: $flutterPath" -ForegroundColor Cyan

# Check if Flutter exists
if (Test-Path $flutterExe) {
    Write-Host "✅ Flutter found at: $flutterExe" -ForegroundColor Green
} else {
    Write-Host "❌ Flutter NOT found at: $flutterExe" -ForegroundColor Red
    Write-Host "📂 Checking Downloads folder..." -ForegroundColor Yellow
    Get-ChildItem "C:\Users\<USER>\Downloads" | Where-Object { $_.Name -like "*flutter*" }
    exit 1
}

# Navigate to project
Set-Location "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
Write-Host "📂 Current directory: $(Get-Location)" -ForegroundColor Cyan

# Check Flutter version
Write-Host "🔍 Checking Flutter version..." -ForegroundColor Yellow
try {
    & $flutterExe --version
    Write-Host "✅ Flutter version check successful" -ForegroundColor Green
} catch {
    Write-Host "❌ Flutter version check failed: $_" -ForegroundColor Red
}

# Check Flutter doctor
Write-Host "🩺 Running Flutter doctor..." -ForegroundColor Yellow
try {
    & $flutterExe doctor
    Write-Host "✅ Flutter doctor completed" -ForegroundColor Green
} catch {
    Write-Host "❌ Flutter doctor failed: $_" -ForegroundColor Red
}

Write-Host "🎯 Attempting to run the app..." -ForegroundColor Green
try {
    & $flutterExe run -d web-server --web-port=8080 --verbose
} catch {
    Write-Host "❌ App failed to run: $_" -ForegroundColor Red
}
