# Flutter Setup Guide

Since Flutter is not currently installed on your system, follow these steps to set up the development environment:

## 1. Install Flutter

### Windows (Your Current System)

1. **Download Flutter SDK**
   - Go to https://flutter.dev/docs/get-started/install/windows
   - Download the latest stable release
   - Extract to `C:\flutter` (or your preferred location)

2. **Update PATH Environment Variable**
   - Open "Environment Variables" in Windows
   - Add `C:\flutter\bin` to your PATH
   - Restart your terminal/command prompt

3. **Verify Installation**
   ```bash
   flutter --version
   flutter doctor
   ```

## 2. Install Required Tools

### Android Development
1. **Android Studio**
   - Download from https://developer.android.com/studio
   - Install with default settings
   - Open Android Studio and install Android SDK

2. **Accept Android Licenses**
   ```bash
   flutter doctor --android-licenses
   ```

### iOS Development (Optional - macOS only)
1. **Xcode** (macOS only)
   - Install from Mac App Store
   - Run `sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer`

## 3. Setup Your Project

1. **Navigate to Project Directory**
   ```bash
   cd "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
   ```

2. **Get Dependencies**
   ```bash
   flutter pub get
   ```

3. **Check Setup**
   ```bash
   flutter doctor
   ```

4. **Run the App**
   ```bash
   # For Android
   flutter run

   # For specific device
   flutter devices
   flutter run -d <device-id>
   ```

## 4. Development Environment

### VS Code (Recommended)
1. Install VS Code
2. Install Flutter extension
3. Install Dart extension
4. Open project folder in VS Code

### Android Studio
1. Install Flutter plugin
2. Install Dart plugin
3. Open project in Android Studio

## 5. Testing the App

### Run on Emulator
```bash
# List available devices
flutter devices

# Run on Android emulator
flutter run

# Run in debug mode
flutter run --debug

# Run in release mode
flutter run --release
```

### Build APK
```bash
# Debug APK
flutter build apk --debug

# Release APK
flutter build apk --release
```

## 6. Troubleshooting

### Common Issues

1. **Flutter not recognized**
   - Ensure Flutter is in your PATH
   - Restart terminal/command prompt

2. **Android licenses not accepted**
   ```bash
   flutter doctor --android-licenses
   ```

3. **No devices available**
   - Start Android emulator
   - Connect physical device with USB debugging enabled

4. **Gradle build errors**
   - Clean and rebuild:
   ```bash
   flutter clean
   flutter pub get
   flutter run
   ```

### Getting Help
- Run `flutter doctor -v` for detailed diagnostics
- Check Flutter documentation: https://flutter.dev/docs
- Flutter community: https://flutter.dev/community

## 7. Project-Specific Setup

After Flutter is installed and working:

1. **Generate App Icons**
   ```bash
   flutter pub run flutter_launcher_icons:main
   ```

2. **Run Tests**
   ```bash
   flutter test
   ```

3. **Analyze Code**
   ```bash
   flutter analyze
   ```

## Next Steps

Once you have Flutter installed and the app running:

1. Test the date conversion functionality
2. Try the age calculator with different dates
3. Toggle between light and dark modes
4. Test on different screen sizes
5. Build and install the APK on a physical device

The app is designed to work offline and includes all necessary dependencies for Nepali date conversion.
