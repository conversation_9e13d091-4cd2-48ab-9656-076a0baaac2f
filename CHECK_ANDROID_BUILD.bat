@echo off
title Check Android Build Progress
color 0A

echo.
echo ========================================
echo    ANDROID BUILD STATUS CHECKER
echo ========================================
echo.

echo [1] Check Flutter devices
echo [2] Check build progress
echo [3] Try to run app again
echo [4] Open Android emulator
echo [5] Exit
echo.
set /p choice="Choose option (1-5): "

if "%choice%"=="1" goto check_devices
if "%choice%"=="2" goto check_progress
if "%choice%"=="3" goto run_app
if "%choice%"=="4" goto open_emulator
if "%choice%"=="5" goto exit

:check_devices
echo.
echo Checking available devices...
flutter devices
echo.
echo If you see your emulator (emulator-5554), it's ready!
pause
goto menu

:check_progress
echo.
echo Checking if app is running...
echo.
echo Look at your Android emulator screen:
echo ✅ If you see "Nepali Date Converter" app - SUCCESS!
echo ⏳ If still showing Android home screen - Still building
echo ❌ If you see error messages - Build failed
echo.
echo Current Flutter processes:
tasklist | findstr flutter
echo.
pause
goto menu

:run_app
echo.
echo Running app on Android emulator...
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"
echo.
echo Target: emulator-5554 (Android 13)
echo This may take 5-10 minutes on first run...
echo.
flutter run -d emulator-5554
pause
goto menu

:open_emulator
echo.
echo Opening Android emulator...
echo.
echo If emulator is not visible:
echo 1. Open Android Studio
echo 2. Go to Tools → AVD Manager
echo 3. Click play button next to your virtual device
echo.
start "" "emulator" -avd Pixel_7_API_33 2>nul || echo "Starting emulator via Android Studio..."
pause
goto menu

:exit
echo.
echo Goodbye!
exit

:menu
cls
goto start

:start
echo.
echo ========================================
echo    ANDROID BUILD STATUS CHECKER
echo ========================================
echo.

echo [1] Check Flutter devices
echo [2] Check build progress
echo [3] Try to run app again
echo [4] Open Android emulator
echo [5] Exit
echo.
set /p choice="Choose option (1-5): "

if "%choice%"=="1" goto check_devices
if "%choice%"=="2" goto check_progress
if "%choice%"=="3" goto run_app
if "%choice%"=="4" goto open_emulator
if "%choice%"=="5" goto exit
goto menu
