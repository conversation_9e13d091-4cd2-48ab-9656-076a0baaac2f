import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Theme Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.palette,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Appearance',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Consumer<ThemeProvider>(
                      builder: (context, themeProvider, child) {
                        return SwitchListTile(
                          title: const Text('Dark Mode'),
                          subtitle: Text(
                            themeProvider.isDarkMode
                                ? 'Dark theme enabled'
                                : 'Light theme enabled',
                          ),
                          value: themeProvider.isDarkMode,
                          onChanged: (value) {
                            themeProvider.toggleTheme();
                          },
                          secondary: Icon(
                            themeProvider.isDarkMode
                                ? Icons.dark_mode
                                : Icons.light_mode,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // App Info Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'About',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      title: const Text('App Version'),
                      subtitle: const Text('1.0.0'),
                      leading: const Icon(Icons.apps),
                    ),
                    ListTile(
                      title: const Text('Developer'),
                      subtitle: const Text('Nepali Date Converter Team'),
                      leading: const Icon(Icons.code),
                    ),
                    ListTile(
                      title: const Text('Description'),
                      subtitle: const Text(
                        'A modern Flutter app for converting Nepali and English dates, and calculating age with detailed statistics.',
                      ),
                      leading: const Icon(Icons.description),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Features Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Features',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const ListTile(
                      title: Text('Date Conversion'),
                      subtitle: Text('Convert between Nepali (BS) and English (AD) dates'),
                      leading: Icon(Icons.swap_horiz),
                    ),
                    const ListTile(
                      title: Text('Age Calculator'),
                      subtitle: Text('Calculate exact age with detailed time statistics'),
                      leading: Icon(Icons.cake),
                    ),
                    const ListTile(
                      title: Text('Dark Mode'),
                      subtitle: Text('Toggle between light and dark themes'),
                      leading: Icon(Icons.dark_mode),
                    ),
                    const ListTile(
                      title: Text('Offline Support'),
                      subtitle: Text('Works completely offline'),
                      leading: Icon(Icons.offline_bolt),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
