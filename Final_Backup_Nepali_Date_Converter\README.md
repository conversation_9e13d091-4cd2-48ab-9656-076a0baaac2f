# Nepali Date Converter - Final Project

## 📱 Project Overview
A modern Flutter application for Nepali Date Converter & Age Calculator with Material Design 3 UI, dark mode support, and comprehensive functionality.

## ✅ Features Implemented

### 🔄 Date Converter
- **BS to AD Conversion**: Convert <PERSON><PERSON><PERSON> dates to Gregorian calendar
- **AD to BS Conversion**: Convert Gregorian dates to <PERSON><PERSON><PERSON>
- **Copy Functionality**: <PERSON><PERSON> converted dates to clipboard
- **Real-time Conversion**: Instant conversion as you type

### 📅 Age Calculator
- **Comprehensive Age Display**: Years, months, days, hours, minutes, seconds
- **Multiple Formats**: Age in different time units
- **Copy Results**: Copy calculated age to clipboard
- **Detailed Breakdown**: Complete age analysis

### 🎨 UI/UX Features
- **Material Design 3**: Modern, clean interface
- **Dark Mode Support**: Complete light/dark theme toggle
- **Custom Colors**: Primary (#2D9CDB), Accent (#27AE60)
- **Responsive Design**: Works on all screen sizes
- **Professional Layout**: Card-based sections with shadows

### 📱 App Features
- **Current Date/Time Display**: Shows today's Nepali and English date/time
- **3-Dot Menu**: Privacy Policy, Rate App, About options
- **Custom App Icon**: Personalized launcher icon from assets/appicon.png
- **Professional AppBar**: Clean navigation with theme toggle

### 🔒 Privacy Policy System
- **Smart Loading**: WebView first, Chrome Custom Tabs fallback
- **Mobile Optimized**: In-app WebView for best experience
- **Web Compatible**: Automatic browser opening for web platform
- **Error Handling**: Graceful fallback with retry options
- **No Error Screens**: Seamless user experience

## 🛠️ Technical Stack

### 📦 Dependencies
```yaml
dependencies:
  flutter: sdk
  provider: ^6.1.1          # State management
  nepali_utils: ^3.0.5      # Nepali date utilities
  cupertino_icons: ^1.0.6   # iOS-style icons
  webview_flutter: ^4.4.2   # In-app web browsing
  url_launcher: ^6.3.1      # External URL handling

dev_dependencies:
  flutter_launcher_icons: ^0.13.1  # Custom app icon generation
```

### 🎯 Architecture
- **Provider Pattern**: State management with Provider package
- **Widget Separation**: Modular widget structure
- **Theme System**: Centralized theme management
- **Constants**: Organized constants and utilities

## 📁 Project Structure
```
lib/
├── main.dart                    # App entry point
├── screens/
│   ├── home_screen.dart        # Main app screen
│   └── privacy_policy_page.dart # Privacy policy WebView
├── widgets/
│   ├── current_date_time_widget.dart  # Date/time display
│   ├── date_converter_widget.dart     # Date conversion
│   ├── age_calculator_widget.dart     # Age calculation
│   └── app_menu.dart                  # 3-dot menu
├── providers/
│   └── theme_provider.dart     # Theme state management
└── utils/
    ├── app_constants.dart      # App constants
    └── app_theme.dart          # Theme definitions

assets/
├── appicon.png                 # Custom launcher icon
├── icon/                       # Icon assets
└── images/                     # Image assets

android/                        # Android-specific files
├── app/src/main/res/mipmap-*/  # Generated launcher icons
└── ...                         # Android configuration
```

## 🚀 APK Files

### 📱 Release APK (Production Ready)
- **File**: `APK_Files/app-release-final.apk`
- **Size**: 20.2MB
- **Features**: All functionality, custom icon, optimized
- **Target**: Production deployment, Play Store ready

### 🧪 Debug APK (Development)
- **File**: `APK_Files/app-debug-final.apk`
- **Features**: All functionality with debug capabilities
- **Target**: Testing and development

## 🎯 Privacy Policy Implementation

### 📱 Mobile Experience (APK)
1. **WebView First**: Loads Privacy Policy inside the app
2. **Chrome Custom Tabs Fallback**: If WebView fails
3. **Direct Navigation**: Returns to home screen after viewing
4. **No Error Screens**: Seamless user experience

### 🌐 Web Experience (Browser)
1. **Platform Detection**: Automatically detects web platform
2. **Direct Browser Opening**: Opens Privacy Policy in new tab
3. **No WebView Attempt**: Skips WebView (not supported in browsers)

### 🔗 Privacy Policy URL
```
https://technokdapp.blogspot.com/2025/05/privacy-policy-date-converter-and-age.html
```

## 🎨 Custom App Icon

### 📱 Implementation
- **Source**: `assets/appicon.png`
- **Package**: `flutter_launcher_icons: ^0.13.1`
- **Target**: Android only (iOS excluded as requested)
- **Generated Sizes**: All Android mipmap densities

### 🔧 Configuration
```yaml
flutter_icons:
  android: true
  ios: false
  image_path: "assets/appicon.png"
```

## 🌙 Theme System

### 🎨 Light Theme
- **Primary**: #2D9CDB (Blue)
- **Accent**: #27AE60 (Green)
- **Background**: White
- **Cards**: Light gray with shadows

### 🌙 Dark Theme
- **Primary**: #2D9CDB (Blue)
- **Accent**: #27AE60 (Green)
- **Background**: #121212 (Dark)
- **Cards**: #1E1E1E (Dark gray)

## 🔧 Build Instructions

### 📱 Prerequisites
- Flutter SDK 3.32.0+
- Android Studio with Android SDK
- Java 17 (JDK)

### 🚀 Build Commands
```bash
# Get dependencies
flutter pub get

# Generate launcher icons
flutter pub run flutter_launcher_icons:main

# Build debug APK
flutter build apk --debug

# Build release APK
flutter build apk --release
```

## 📱 Installation
1. Enable "Unknown Sources" in Android settings
2. Install APK file on Android device
3. Launch "Nepali Date Converter" app
4. Enjoy all features including Privacy Policy

## 🎊 Project Status
- **✅ Complete**: All features implemented and tested
- **✅ Production Ready**: Release APK available
- **✅ Custom Branding**: Personalized app icon
- **✅ Privacy Policy Fixed**: Smart WebView + fallback system
- **✅ Professional UI**: Material Design 3 with dark mode
- **✅ Play Store Ready**: Optimized for distribution

---

**Final Version**: 1.0.0+1  
**Build Date**: May 31, 2025  
**APK Size**: 20.2MB  
**Target Platform**: Android  
**Status**: Production Ready 🚀
