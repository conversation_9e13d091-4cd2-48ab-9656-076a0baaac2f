class AgeCalculation {
  final int years;
  final int months;
  final int days;
  final int totalMonths;
  final int totalWeeks;
  final int totalDays;
  final int totalHours;
  final int totalMinutes;
  final int totalSeconds;

  AgeCalculation({
    required this.years,
    required this.months,
    required this.days,
    required this.totalMonths,
    required this.totalWeeks,
    required this.totalDays,
    required this.totalHours,
    required this.totalMinutes,
    required this.totalSeconds,
  });

  @override
  String toString() {
    return '$years years, $months months, $days days';
  }

  String get formattedAge => '$years years, $months months, $days days';
  
  String get totalMonthsFormatted => totalMonths.toString();
  String get totalWeeksFormatted => totalWeeks.toString();
  String get totalDaysFormatted => totalDays.toString();
  String get totalHoursFormatted => totalHours.toString();
  String get totalMinutesFormatted => totalMinutes.toString();
  String get totalSecondsFormatted => totalSeconds.toString();
}
