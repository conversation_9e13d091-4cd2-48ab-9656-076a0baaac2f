import 'package:nepali_utils/nepali_utils.dart';

void main() {
  print('🧪 Testing Nepali Date Conversion');
  print('================================');
  
  // Test Case 1: BS to AD
  print('\n📅 Test Case 1: BS 2081/01/15 → AD');
  try {
    final nepaliDate = NepaliDateTime(2081, 1, 15);
    final adDate = nepaliDate.toDateTime();
    print('✅ BS: 2081/01/15');
    print('✅ AD: ${adDate.year}/${adDate.month.toString().padLeft(2, '0')}/${adDate.day.toString().padLeft(2, '0')}');
    print('Expected: 2024/04/27');
    
    if (adDate.year == 2024 && adDate.month == 4 && adDate.day == 27) {
      print('🎉 TEST PASSED!');
    } else {
      print('❌ TEST FAILED!');
    }
  } catch (e) {
    print('❌ Error: $e');
  }
  
  // Test Case 2: AD to BS
  print('\n📅 Test Case 2: AD 2024/04/27 → BS');
  try {
    final adDate = DateTime(2024, 4, 27);
    final nepaliDate = adDate.toNepaliDateTime();
    print('✅ AD: 2024/04/27');
    print('✅ BS: ${nepaliDate.year}/${nepaliDate.month.toString().padLeft(2, '0')}/${nepaliDate.day.toString().padLeft(2, '0')}');
    print('Expected: 2081/01/15');
    
    if (nepaliDate.year == 2081 && nepaliDate.month == 1 && nepaliDate.day == 15) {
      print('🎉 TEST PASSED!');
    } else {
      print('❌ TEST FAILED!');
    }
  } catch (e) {
    print('❌ Error: $e');
  }
  
  // Additional Test Cases
  print('\n📅 Additional Test Cases:');
  
  // Current date
  final now = DateTime.now();
  final nepaliNow = now.toNepaliDateTime();
  print('Current AD: ${now.year}/${now.month}/${now.day}');
  print('Current BS: ${nepaliNow.year}/${nepaliNow.month}/${nepaliNow.day}');
  
  // Test some edge cases
  print('\n🔍 Edge Cases:');
  
  // New Year dates
  try {
    final nepaliNewYear = NepaliDateTime(2081, 1, 1);
    final adNewYear = nepaliNewYear.toDateTime();
    print('BS New Year 2081/01/01 → AD ${adNewYear.year}/${adNewYear.month}/${adNewYear.day}');
  } catch (e) {
    print('Error with BS New Year: $e');
  }
  
  try {
    final adNewYear = DateTime(2024, 1, 1);
    final nepaliFromAD = adNewYear.toNepaliDateTime();
    print('AD New Year 2024/01/01 → BS ${nepaliFromAD.year}/${nepaliFromAD.month}/${nepaliFromAD.day}');
  } catch (e) {
    print('Error with AD New Year: $e');
  }
  
  print('\n✅ Conversion test completed!');
}
