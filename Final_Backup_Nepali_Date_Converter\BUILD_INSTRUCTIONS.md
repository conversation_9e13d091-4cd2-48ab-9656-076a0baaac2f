# Build Instructions - Nepali Date Converter

## 🛠️ Prerequisites

### 📱 Required Software
1. **Flutter SDK**: Version 3.32.0 or higher
   - Download from: https://flutter.dev/docs/get-started/install
   - Add to PATH environment variable

2. **Android Studio**: Latest version
   - Download from: https://developer.android.com/studio
   - Install Android SDK and build tools
   - Configure Android emulator (optional)

3. **Java Development Kit (JDK)**: Version 17
   - Download from: https://www.oracle.com/java/technologies/downloads/
   - Set JAVA_HOME environment variable

4. **Git**: For version control
   - Download from: https://git-scm.com/downloads

### 🔧 Environment Setup

#### Windows
```bash
# Set JAVA_HOME (adjust path as needed)
$env:JAVA_HOME="C:\Program Files\Microsoft\jdk-17.0.15.6-hotspot"

# Verify Flutter installation
flutter doctor
```

#### macOS/Linux
```bash
# Set JAVA_HOME (adjust path as needed)
export JAVA_HOME="/usr/lib/jvm/java-17-openjdk"

# Verify Flutter installation
flutter doctor
```

## 🚀 Build Process

### 📦 Step 1: Get Dependencies
```bash
# Navigate to project directory
cd "Nepali Date Converter"

# Get all Flutter dependencies
flutter pub get
```

### 🎨 Step 2: Generate Launcher Icons
```bash
# Generate custom app icons from assets/appicon.png
flutter pub run flutter_launcher_icons:main

# Alternative command (newer Flutter versions)
dart run flutter_launcher_icons
```

### 🧪 Step 3: Build Debug APK (for testing)
```bash
# Build debug APK with debugging capabilities
flutter build apk --debug

# Output: build/app/outputs/flutter-apk/app-debug.apk
```

### 🚀 Step 4: Build Release APK (for production)
```bash
# Build optimized release APK
flutter build apk --release

# Output: build/app/outputs/flutter-apk/app-release.apk
```

### 📱 Step 5: Install APK
```bash
# Install on connected Android device
flutter install

# Or manually install APK file on device
# Enable "Unknown Sources" in Android settings first
```

## 🔧 Troubleshooting

### ❌ Common Issues

#### 1. Kotlin Version Compatibility
**Error**: "Module was compiled with an incompatible version of Kotlin"
**Solution**: This is a warning and doesn't affect the build. APK will still be generated successfully.

#### 2. Java Version Issues
**Error**: "Could not determine java version"
**Solution**: 
```bash
# Ensure JAVA_HOME is set correctly
echo $JAVA_HOME  # Linux/macOS
echo $env:JAVA_HOME  # Windows PowerShell

# Use Java 17 specifically
$env:JAVA_HOME="C:\Program Files\Microsoft\jdk-17.0.15.6-hotspot"
```

#### 3. Flutter Doctor Issues
**Error**: Various Flutter doctor warnings
**Solution**:
```bash
# Run Flutter doctor to see issues
flutter doctor

# Accept Android licenses
flutter doctor --android-licenses

# Update Flutter
flutter upgrade
```

#### 4. Gradle Build Failures
**Error**: Gradle build errors
**Solution**:
```bash
# Clean build cache
flutter clean

# Get dependencies again
flutter pub get

# Try building again
flutter build apk --release
```

### 🔍 Verification Steps

#### ✅ Check Build Success
1. **APK File Created**: Verify APK exists in `build/app/outputs/flutter-apk/`
2. **File Size**: Release APK should be ~20MB
3. **Installation**: APK should install on Android device without errors
4. **App Launch**: App should open with custom icon
5. **Features Test**: All features should work including Privacy Policy

#### 📱 Testing Checklist
- [ ] App launches successfully
- [ ] Custom launcher icon appears
- [ ] Date converter works (BS ↔ AD)
- [ ] Age calculator functions properly
- [ ] Dark mode toggle works
- [ ] Copy functionality works
- [ ] 3-dot menu opens
- [ ] Privacy Policy opens (WebView or Chrome Custom Tab)
- [ ] All UI elements display correctly

## 📁 Output Files

### 🎯 Generated APK Files
```
build/app/outputs/flutter-apk/
├── app-debug.apk      # Debug version (larger, with debugging)
└── app-release.apk    # Release version (optimized, production-ready)
```

### 🎨 Generated Icon Files
```
android/app/src/main/res/
├── mipmap-hdpi/ic_launcher.png     # 72x72px
├── mipmap-mdpi/ic_launcher.png     # 48x48px
├── mipmap-xhdpi/ic_launcher.png    # 96x96px
├── mipmap-xxhdpi/ic_launcher.png   # 144x144px
└── mipmap-xxxhdpi/ic_launcher.png  # 192x192px
```

## 🚀 Deployment

### 📱 Direct Installation
1. Copy APK file to Android device
2. Enable "Install from Unknown Sources"
3. Tap APK file to install
4. Launch app from home screen

### 🏪 Google Play Store
1. Use `app-release.apk` for Play Store submission
2. Create Play Console account
3. Upload APK through Play Console
4. Complete store listing information
5. Submit for review

## 🔧 Development Commands

### 🧪 Development Workflow
```bash
# Run app in development mode
flutter run

# Hot reload (during development)
r

# Hot restart (during development)
R

# Run on specific device
flutter run -d <device-id>

# Run on Chrome (web testing)
flutter run -d chrome
```

### 📊 Analysis Commands
```bash
# Analyze code quality
flutter analyze

# Run tests
flutter test

# Check dependencies
flutter pub deps

# Check for outdated packages
flutter pub outdated
```

## 📝 Notes

### ⚠️ Important Considerations
1. **Custom Icon**: Ensure `assets/appicon.png` exists before building
2. **Privacy Policy URL**: Verify URL is accessible before building
3. **Permissions**: App requires internet permission for Privacy Policy
4. **Testing**: Test on multiple Android versions if possible
5. **Size**: Release APK is optimized and smaller than debug APK

### 🎯 Build Optimization
- **Tree Shaking**: Unused code automatically removed
- **Icon Optimization**: Icons optimized for different screen densities
- **Asset Compression**: Images and assets compressed for smaller APK
- **Code Obfuscation**: Release build includes code obfuscation

---

**Last Updated**: May 31, 2025  
**Flutter Version**: 3.32.0  
**Target Android**: API 21+ (Android 5.0+)  
**Build Status**: ✅ Production Ready
