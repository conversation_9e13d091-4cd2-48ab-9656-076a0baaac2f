@echo off
title Run on Android - Nepali Date Converter
color 0A

echo.
echo ========================================
echo    RUN ON ANDROID DEVICE/EMULATOR
echo    Nepali Date Converter
echo ========================================
echo.

echo [1/4] Checking available devices...
echo.
flutter devices

echo.
echo [2/4] Choose your option:
echo.
echo [1] Run on connected Android device/emulator
echo [2] Build APK for manual installation
echo [3] Start web server for mobile browser
echo [4] Check Android setup status
echo [5] Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto run_android
if "%choice%"=="2" goto build_apk
if "%choice%"=="3" goto web_mobile
if "%choice%"=="4" goto check_setup
if "%choice%"=="5" goto exit

:run_android
echo.
echo [3/4] Navigating to project...
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"

echo.
echo [4/4] Running app on Android...
echo.
echo ========================================
echo  Starting app on Android device...
echo  This may take a few minutes...
echo ========================================
echo.
flutter run

pause
goto end

:build_apk
echo.
echo Starting APK build process...
call BUILD_ANDROID_APK.bat
goto end

:web_mobile
echo.
echo [3/4] Starting web server for mobile access...
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"

echo.
echo [4/4] Starting web server...
echo.
echo ========================================
echo  Web server starting...
echo  Access from mobile browser at:
echo  http://YOUR_COMPUTER_IP:8080
echo ========================================
echo.
echo To find your IP address, open another command prompt and run: ipconfig
echo.
flutter run -d web-server --web-port=8080 --web-hostname=0.0.0.0

pause
goto end

:check_setup
echo.
echo ========================================
echo  ANDROID SETUP STATUS
echo ========================================
echo.
echo Checking Flutter doctor...
flutter doctor

echo.
echo Checking available devices...
flutter devices

echo.
echo ========================================
echo  SETUP RECOMMENDATIONS
echo ========================================
echo.
echo If no Android devices found:
echo 1. Install Android Studio from https://developer.android.com/studio
echo 2. Create virtual device in AVD Manager
echo 3. Or connect physical Android device with USB debugging
echo.
echo If you see "Android toolchain" issues:
echo 1. Run: flutter doctor --android-licenses
echo 2. Accept all licenses
echo.
pause
goto end

:exit
echo.
echo Goodbye!
goto end

:end
