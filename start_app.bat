@echo off
title Nepali Date Converter - Flutter App
color 0A

echo.
echo ========================================
echo    NEPALI DATE CONVERTER APP
echo ========================================
echo.

echo [1/5] Setting up Flutter environment...
set FLUTTER_BIN=C:\Users\<USER>\Downloads\flutter_windows_3.32.0-stable\flutter\bin
set PATH=%PATH%;%FLUTTER_BIN%

echo [2/5] Navigating to project directory...
cd /d "C:\Users\<USER>\Documents\augment-projects\Nepali Date Converter"

echo [3/5] Checking Flutter installation...
"%FLUTTER_BIN%\flutter.bat" --version
if errorlevel 1 (
    echo ERROR: Flutter not found! Please check the path.
    pause
    exit /b 1
)

echo [4/5] Installing dependencies...
"%FLUTTER_BIN%\flutter.bat" pub get
if errorlevel 1 (
    echo ERROR: Failed to get dependencies!
    pause
    exit /b 1
)

echo [5/5] Starting the app...
echo.
echo ========================================
echo  App will open at: http://localhost:8080
echo  Press Ctrl+C to stop the app
echo ========================================
echo.

"%FLUTTER_BIN%\flutter.bat" run -d web-server --web-port=8080 --web-hostname=localhost

pause
