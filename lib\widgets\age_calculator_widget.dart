import 'package:flutter/material.dart';

class AgeCalculatorWidget extends StatefulWidget {
  const AgeCalculatorWidget({super.key});

  @override
  State<AgeCalculatorWidget> createState() => _AgeCalculatorWidgetState();
}

class _AgeCalculatorWidgetState extends State<AgeCalculatorWidget> {
  final TextEditingController _yearController = TextEditingController();
  final TextEditingController _monthController = TextEditingController();
  final TextEditingController _dayController = TextEditingController();
  Map<String, dynamic>? _ageDetails;
  String? _errorMessage;

  @override
  void dispose() {
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    super.dispose();
  }

  void _clear() {
    setState(() {
      _yearController.clear();
      _monthController.clear();
      _dayController.clear();
      _ageDetails = null;
      _errorMessage = null;
    });
  }

  void _calculateAge() {
    setState(() {
      _errorMessage = null;
      _ageDetails = null;
    });

    try {
      final year = int.parse(_yearController.text);
      final month = int.parse(_monthController.text);
      final day = int.parse(_dayController.text);

      // Validate input
      if (year < 1900 || year > DateTime.now().year) {
        setState(() {
          _errorMessage =
              'Year must be between 1900 and ${DateTime.now().year}';
        });
        return;
      }
      if (month < 1 || month > 12) {
        setState(() {
          _errorMessage = 'Month must be between 1 and 12';
        });
        return;
      }
      if (day < 1 || day > 31) {
        setState(() {
          _errorMessage = 'Day must be between 1 and 31';
        });
        return;
      }

      final birthDate = DateTime(year, month, day);
      final now = DateTime.now();

      if (birthDate.isAfter(now)) {
        setState(() {
          _errorMessage = 'Birth date cannot be in the future';
        });
        return;
      }

      // Calculate years, months, days
      int years = now.year - birthDate.year;
      int months = now.month - birthDate.month;
      int days = now.day - birthDate.day;

      // Adjust for negative days
      if (days < 0) {
        months--;
        final lastMonth = DateTime(now.year, now.month, 0);
        days += lastMonth.day;
      }

      // Adjust for negative months
      if (months < 0) {
        years--;
        months += 12;
      }

      // Calculate total units
      final totalDays = now.difference(birthDate).inDays;
      final totalWeeks = totalDays ~/ 7;
      final totalHours = totalDays * 24;
      final totalMinutes = totalHours * 60;

      setState(() {
        _ageDetails = {
          'years': years,
          'months': months,
          'days': days,
          'totalDays': totalDays,
          'totalWeeks': totalWeeks,
          'totalHours': totalHours,
          'totalMinutes': totalMinutes,
        };
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid date format. Please enter valid numbers.';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Input fields
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _yearController,
                decoration: const InputDecoration(
                  labelText: 'Year',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _monthController,
                decoration: const InputDecoration(
                  labelText: 'Month',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _dayController,
                decoration: const InputDecoration(
                  labelText: 'Day',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Calculate Age button (fixed height)
        SizedBox(
          width: double.infinity,
          height: 44,
          child: ElevatedButton(
            onPressed: _calculateAge,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'CALCULATE AGE',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Clear button
        TextButton(
          onPressed: _clear,
          child: Text(
            'Clear',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Error Message or Age Results
        if (_errorMessage != null)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.error, color: Colors.red, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red, fontSize: 14),
                  ),
                ),
              ],
            ),
          )
        else if (_ageDetails != null) ...[
          // Main Age Display
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
            ),
            child: Column(
              children: [
                const Icon(Icons.cake, color: Colors.green, size: 24),
                const SizedBox(height: 8),
                Text(
                  'Your Age',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${_ageDetails!['years']} Years, ${_ageDetails!['months']} Months, ${_ageDetails!['days']} Days',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Additional Details
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Also equals to:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        '${_ageDetails!['totalDays']}',
                        'Days',
                        Icons.today,
                        Colors.orange,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        '${_ageDetails!['totalWeeks']}',
                        'Weeks',
                        Icons.calendar_view_week,
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        '${_ageDetails!['totalHours']}',
                        'Hours',
                        Icons.access_time,
                        Colors.purple,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        '${_ageDetails!['totalMinutes']}',
                        'Minutes',
                        Icons.timer,
                        Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatCard(
      String value, String label, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
              fontSize: 12,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
