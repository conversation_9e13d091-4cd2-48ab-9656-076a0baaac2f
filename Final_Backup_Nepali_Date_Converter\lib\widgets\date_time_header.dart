import 'package:flutter/material.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'dart:async';

class DateTimeHeader extends StatefulWidget {
  final bool isInAppBar;

  const DateTimeHeader({super.key, this.isInAppBar = false});

  @override
  State<DateTimeHeader> createState() => _DateTimeHeaderState();
}

class _DateTimeHeaderState extends State<DateTimeHeader> {
  late Timer _timer;
  DateTime _currentTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    // Update time every second
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _currentTime = DateTime.now();
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  String _getNepaliDate() {
    final nepaliDate = _currentTime.toNepaliDateTime();
    final englishWeekdays = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday'
    ];
    final englishMonths = [
      'Baishakh',
      'Jestha',
      'Ashadh',
      'Shrawan',
      'Bhadra',
      'Ashwin',
      'Kartik',
      'Mangsir',
      'Poush',
      'Magh',
      'Falgun',
      'Chaitra'
    ];

    final weekday = englishWeekdays[nepaliDate.weekday - 1];
    final month = englishMonths[nepaliDate.month - 1];
    final day = nepaliDate.day;
    final year = nepaliDate.year;

    return '$weekday, $month $day, $year';
  }

  String _getEnglishDateAndTime() {
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];

    final month = months[_currentTime.month - 1];
    final day = _currentTime.day;
    final year = _currentTime.year;

    // Format time
    final hour = _currentTime.hour;
    final minute = _currentTime.minute.toString().padLeft(2, '0');
    final hour12 = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    final amPm = hour >= 12 ? 'pm' : 'am';

    return '$month $day, $year – $hour12:$minute $amPm';
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isInAppBar) {
      // AppBar format: "Thursday, Jestha 15, 2082 | May 29, 2025 – 10:33 PM"
      return Text(
        '${_getNepaliDate()} | ${_getEnglishDateAndTime()}',
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
      );
    } else {
      // Regular format for body content
      return Column(
        children: [
          // Nepali Date - Bold at top (like "Wednesday, Jestha 15, 2082")
          Text(
            _getNepaliDate(),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),

          // English Date and Time - Smaller below (like "May 28, 2025 – 11:17 pm")
          Text(
            _getEnglishDateAndTime(),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }
  }
}
