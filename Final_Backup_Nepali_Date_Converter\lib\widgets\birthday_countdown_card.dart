import 'package:flutter/material.dart';
import 'package:nepali_utils/nepali_utils.dart';
import '../utils/age_calculator.dart';

class BirthdayCountdownCard extends StatefulWidget {
  final DateTime birthDate;
  final bool useNepaliDate;

  const BirthdayCountdownCard({
    super.key,
    required this.birthDate,
    required this.useNepaliDate,
  });

  @override
  State<BirthdayCountdownCard> createState() => _BirthdayCountdownCardState();
}

class _BirthdayCountdownCardState extends State<BirthdayCountdownCard>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  Duration? _timeUntilBirthday;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _updateCountdown();
    _pulseController.repeat(reverse: true);

    // Update countdown every minute
    Stream.periodic(const Duration(minutes: 1)).listen((_) {
      if (mounted) {
        _updateCountdown();
      }
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _updateCountdown() {
    setState(() {
      _timeUntilBirthday =
          AgeCalculatorUtil.getTimeUntilNextBirthday(widget.birthDate);
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_timeUntilBirthday == null) {
      return const SizedBox.shrink();
    }

    final isToday = _timeUntilBirthday!.inDays == 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Icon(
                        isToday ? Icons.celebration : Icons.cake,
                        color: isToday
                            ? Theme.of(context).colorScheme.secondary
                            : Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                    );
                  },
                ),
                const SizedBox(width: 12),
                Text(
                  isToday ? '🎉 Happy Birthday!' : 'Next Birthday',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (isToday) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.secondary.withOpacity(0.2),
                      Theme.of(context).colorScheme.primary.withOpacity(0.2),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Theme.of(context)
                        .colorScheme
                        .secondary
                        .withOpacity(0.5),
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      'Today is your birthday!',
                      style: Theme.of(context)
                              .textTheme
                              .headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ) ??
                          const TextStyle(),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '🎂 Wishing you a wonderful day! 🎂',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Colors.grey[600],
                          ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ] else ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color:
                        Theme.of(context).colorScheme.primary.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      AgeCalculatorUtil.formatDuration(_timeUntilBirthday!),
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'until your next birthday',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Birthday Date Display
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey.withOpacity(0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Next Birthday Date',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[600],
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getNextBirthdayFormatted(),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getNextBirthdayFormatted() {
    final nextBirthday = AgeCalculatorUtil.getNextBirthday(widget.birthDate);

    if (widget.useNepaliDate) {
      final nepaliDate = nextBirthday.toNepaliDateTime();
      return '${nepaliDate.format('EEEE')}, ${nepaliDate.format('MMMM')} ${nepaliDate.day}, ${nepaliDate.year}';
    } else {
      final weekdays = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday'
      ];
      final months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
      ];
      return '${weekdays[nextBirthday.weekday - 1]}, ${months[nextBirthday.month - 1]} ${nextBirthday.day}, ${nextBirthday.year}';
    }
  }
}
